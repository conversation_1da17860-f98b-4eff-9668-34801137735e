/**
 * 测试智能提报API的脚本
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3000';
const TEST_TOKEN = 'your-jwt-token-here'; // 需要替换为实际的JWT token

// 测试数据
const testData = {
  checkCookie: {
    platform: 'xiaohongshu'
  },
  authorInfo: {
    platform: 'xiaohongshu',
    authorId: '6304b0aa000000001200e019'
  },
  authorVideos: {
    platform: 'xiaohongshu',
    authorId: '6304b0aa000000001200e019',
    associateVideos: false
  }
};

/**
 * 创建HTTP客户端
 */
function createClient() {
  return axios.create({
    baseURL: BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${TEST_TOKEN}`
    }
  });
}

/**
 * 测试API端点
 */
async function testAPI(endpoint, data, description) {
  console.log(`\n🧪 测试: ${description}`);
  console.log(`📡 请求: POST ${endpoint}`);
  console.log(`📦 数据: ${JSON.stringify(data, null, 2)}`);
  
  try {
    const client = createClient();
    const response = await client.post(endpoint, data);
    
    console.log(`✅ 成功: ${response.status} ${response.statusText}`);
    console.log(`📄 响应: ${JSON.stringify(response.data, null, 2)}`);
    
    return { success: true, data: response.data };
  } catch (error) {
    console.log(`❌ 失败: ${error.response?.status || 'Network Error'} ${error.response?.statusText || error.message}`);
    
    if (error.response?.data) {
      console.log(`📄 错误响应: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    
    return { success: false, error: error.message };
  }
}

/**
 * 测试所有智能提报API
 */
async function testAllAPIs() {
  console.log('🚀 开始测试智能提报API...');
  console.log('=' .repeat(60));
  
  // 测试1: 检查Cookie状态
  await testAPI(
    '/api/influencer-reports/smart/check-cookie',
    testData.checkCookie,
    '检查Cookie状态'
  );
  
  // 测试2: 获取达人基础信息
  await testAPI(
    '/api/influencer-reports/smart/author-info',
    testData.authorInfo,
    '获取达人基础信息'
  );
  
  // 测试3: 获取达人作品数据
  await testAPI(
    '/api/influencer-reports/smart/author-videos',
    testData.authorVideos,
    '获取达人作品数据'
  );
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 测试完成');
}

/**
 * 测试基础连接
 */
async function testConnection() {
  console.log('🔗 测试服务器连接...');
  
  try {
    const client = createClient();
    const response = await client.get('/health');
    
    console.log(`✅ 服务器连接正常: ${response.status}`);
    console.log(`📄 健康检查: ${JSON.stringify(response.data, null, 2)}`);
    
    return true;
  } catch (error) {
    console.log(`❌ 服务器连接失败: ${error.message}`);
    console.log('💡 请确保服务器正在运行在 http://localhost:3000');
    
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🧪 智能提报API测试工具');
  console.log('=' .repeat(60));
  
  // 检查配置
  if (TEST_TOKEN === 'your-jwt-token-here') {
    console.log('⚠️  请先在脚本中设置有效的JWT token');
    console.log('💡 可以从浏览器开发者工具中获取Authorization header的值');
    return;
  }
  
  // 测试连接
  const connected = await testConnection();
  if (!connected) {
    return;
  }
  
  // 测试API
  await testAllAPIs();
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testAPI,
  testAllAPIs,
  testConnection
};
