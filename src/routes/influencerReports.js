/**
 * 达人提报路由
 *
 * 功能说明：
 * - 提供达人提报的所有API路由
 * - 实现权限控制：普通用户可创建提报，管理员可审核
 * - 支持30天保护期检查和状态管理
 *
 * 权限控制：
 * - 所有路由都需要身份验证
 * - 状态更新路由需要管理员权限
 * - 编辑和删除有特殊权限控制（提报人或管理员）
 *
 * 路由列表：
 * - GET /api/influencer-reports - 获取提报列表
 * - POST /api/influencer-reports - 创建新提报
 * - GET /api/influencer-reports/:id - 获取提报详情
 * - PUT /api/influencer-reports/:id - 更新提报信息
 * - PUT /api/influencer-reports/:id/status - 更新提报状态（管理员）
 * - DELETE /api/influencer-reports/:id - 删除提报
 * - POST /api/influencer-reports/check-protection - 检查30天保护期
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const Router = require('koa-router');
const InfluencerReportController = require('../controllers/influencerReportController');
const { authenticate, requireAdmin } = require('../middleware/auth');

const router = new Router({
  prefix: '/api/influencer-reports'
});

// 所有路由都需要身份验证
router.use(authenticate);

/**
 * @swagger
 * /api/influencer-reports:
 *   get:
 *     summary: 获取达人提报列表
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 达人名称关键词
 *       - in: query
 *         name: platform
 *         schema:
 *           type: string
 *           enum: [xiaohongshu, juxingtu]
 *         description: 平台筛选
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [submitting, resubmitting, need_communication, approved]
 *         description: 状态筛选
 *       - in: query
 *         name: operationManager
 *         schema:
 *           type: string
 *         description: 运营负责人
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 */
router.get('/', InfluencerReportController.getReports);

/**
 * @swagger
 * /api/influencer-reports/check-protection:
 *   post:
 *     summary: 检查30天保护期
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - influencerName
 *               - platform
 *             properties:
 *               influencerName:
 *                 type: string
 *                 description: 达人名称
 *               platform:
 *                 type: string
 *                 enum: [xiaohongshu, juxingtu]
 *                 description: 平台类型
 *     responses:
 *       200:
 *         description: 检查成功
 *       400:
 *         description: 参数错误
 */
router.post('/check-protection', InfluencerReportController.checkProtectionPeriod);

/**
 * @swagger
 * /api/influencer-reports/check-duplicate:
 *   post:
 *     summary: 检查重复提报控制
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - influencerName
 *               - platform
 *               - operationManager
 *             properties:
 *               influencerName:
 *                 type: string
 *               platform:
 *                 type: string
 *                 enum: [xiaohongshu, juxingtu]
 *               operationManager:
 *                 type: string
 *     responses:
 *       200:
 *         description: 检查结果
 *       400:
 *         description: 参数错误
 */
router.post('/check-duplicate', InfluencerReportController.checkDuplicateReportAPI);

/**
 * @swagger
 * /api/influencer-reports/smart/check-cookie:
 *   post:
 *     summary: 检查Cookie状态（智能提报）
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platform
 *             properties:
 *               platform:
 *                 type: string
 *                 enum: [xiaohongshu, juxingtu]
 *                 description: 平台类型
 *     responses:
 *       200:
 *         description: 检查完成
 *       400:
 *         description: 参数错误
 */
router.post('/smart/check-cookie', InfluencerReportController.checkCookieStatus);

/**
 * @swagger
 * /api/influencer-reports/smart/author-info:
 *   post:
 *     summary: 获取达人基础信息（智能提报）
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platform
 *               - authorId
 *             properties:
 *               platform:
 *                 type: string
 *                 enum: [xiaohongshu, juxingtu]
 *                 description: 平台类型
 *               authorId:
 *                 type: string
 *                 description: 达人平台用户ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 达人不存在
 */
router.post('/smart/author-info', InfluencerReportController.getAuthorInfo);

/**
 * @swagger
 * /api/influencer-reports/smart/author-detail:
 *   post:
 *     summary: 获取达人详细信息（智能提报）
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platform
 *               - authorId
 *             properties:
 *               platform:
 *                 type: string
 *                 enum: [xiaohongshu, juxingtu]
 *                 description: 平台类型
 *               authorId:
 *                 type: string
 *                 description: 达人平台用户ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     platform:
 *                       type: string
 *                     platformUserId:
 *                       type: string
 *                     nickname:
 *                       type: string
 *                     avatarUrl:
 *                       type: string
 *                     followersCount:
 *                       type: integer
 *                     playMid:
 *                       type: string
 *                     videoStats:
 *                       type: object
 *                     authorExtInfo:
 *                       type: object
 *                       description: 达人扩展信息（JSON格式）
 *                     rawData:
 *                       type: object
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 达人不存在
 *       503:
 *         description: Cookie配置问题
 */
router.post('/smart/author-detail', InfluencerReportController.getAuthorDetail);

/**
 * @swagger
 * /api/influencer-reports/smart/author-videos:
 *   post:
 *     summary: 获取达人所有作品（智能提报）
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platform
 *               - authorId
 *             properties:
 *               platform:
 *                 type: string
 *                 enum: [xiaohongshu, juxingtu]
 *                 description: 平台类型
 *               authorId:
 *                 type: string
 *                 description: 达人平台用户ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       400:
 *         description: 参数错误
 */
router.post('/smart/author-videos', InfluencerReportController.getAuthorVideos);

/**
 * @swagger
 * /api/influencer-reports:
 *   post:
 *     summary: 创建达人提报
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platform
 *               - influencerName
 *               - operationManager
 *               - selectionReason
 *             properties:
 *               platform:
 *                 type: string
 *                 enum: [xiaohongshu, juxingtu]
 *               influencerName:
 *                 type: string
 *               operationManager:
 *                 type: string
 *               influencerUrl:
 *                 type: string
 *               followersCount:
 *                 type: integer
 *               playMid:
 *                 type: string
 *               selectionReason:
 *                 type: string
 *               platformPrice:
 *                 type: string
 *               cooperationPrice:
 *                 type: string
 *               notes:
 *                 type: string
 *               platformUserId:
 *                 type: string
 *                 description: 平台用户ID
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 参数错误
 *       409:
 *         description: 保护期内无法提报
 */
router.post('/', InfluencerReportController.createReport);

/**
 * @swagger
 * /api/influencer-reports/{id}:
 *   get:
 *     summary: 获取提报详情
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 提报不存在
 */
router.get('/:id', InfluencerReportController.getReportById);

/**
 * @swagger
 * /api/influencer-reports/{id}:
 *   put:
 *     summary: 更新提报信息
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 更新成功
 *       403:
 *         description: 无权限
 *       404:
 *         description: 提报不存在
 */
router.put('/:id', InfluencerReportController.updateReport);

/**
 * @swagger
 * /api/influencer-reports/{id}/status:
 *   put:
 *     summary: 更新提报状态（管理员专用）
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [submitting, resubmitting, need_communication, approved]
 *     responses:
 *       200:
 *         description: 更新成功
 *       403:
 *         description: 需要管理员权限
 */
router.put('/:id/status', requireAdmin, InfluencerReportController.updateReportStatus);

/**
 * @swagger
 * /api/influencer-reports/{id}:
 *   delete:
 *     summary: 删除提报
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: 删除成功
 *       403:
 *         description: 无权限
 *       404:
 *         description: 提报不存在
 */
router.delete('/:id', InfluencerReportController.deleteReport);

/**
 * @swagger
 * /api/influencer-reports/{id}/approve:
 *   put:
 *     summary: 审核通过
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reviewComment
 *             properties:
 *               reviewComment:
 *                 type: string
 *                 description: 审核意见
 *     responses:
 *       200:
 *         description: 审核通过成功
 *       400:
 *         description: 参数错误或状态不允许
 *       403:
 *         description: 无权限
 *       404:
 *         description: 提报不存在
 */
router.put('/:id/approve', requireAdmin, InfluencerReportController.approveReport);

/**
 * @swagger
 * /api/influencer-reports/{id}/reject:
 *   put:
 *     summary: 审核拒绝
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reviewComment
 *             properties:
 *               reviewComment:
 *                 type: string
 *                 description: 审核意见
 *     responses:
 *       200:
 *         description: 审核拒绝成功
 *       400:
 *         description: 参数错误或状态不允许
 *       403:
 *         description: 无权限
 *       404:
 *         description: 提报不存在
 */
router.put('/:id/reject', requireAdmin, InfluencerReportController.rejectReport);

/**
 * @swagger
 * /api/influencer-reports/{id}/need-confirmation:
 *   put:
 *     summary: 需二次确认
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reviewComment
 *             properties:
 *               reviewComment:
 *                 type: string
 *                 description: 审核意见
 *     responses:
 *       200:
 *         description: 标记需二次确认成功
 *       400:
 *         description: 参数错误或状态不允许
 *       403:
 *         description: 无权限
 *       404:
 *         description: 提报不存在
 */
router.put('/:id/need-confirmation', requireAdmin, InfluencerReportController.needConfirmation);

/**
 * @swagger
 * /api/influencer-reports/{id}/resubmit:
 *   put:
 *     summary: 重新提报
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - resubmitReason
 *             properties:
 *               resubmitReason:
 *                 type: string
 *                 description: 重新提报说明/理由
 *     responses:
 *       200:
 *         description: 重新提报成功
 *       400:
 *         description: 参数错误或状态不允许
 *       403:
 *         description: 无权限
 *       404:
 *         description: 提报不存在
 */
router.put('/:id/resubmit', InfluencerReportController.resubmitReport);

/**
 * @swagger
 * /api/influencer-reports/{id}/create-cooperation:
 *   post:
 *     summary: 创建合作对接记录
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 提报记录ID
 *     responses:
 *       201:
 *         description: 合作对接记录创建成功
 *       400:
 *         description: 参数错误或状态不允许
 *       404:
 *         description: 提报不存在
 */
router.post('/:id/create-cooperation', InfluencerReportController.createCooperationRecord);

/**
 * @swagger
 * /api/influencer-reports/{id}/can-create-cooperation:
 *   get:
 *     summary: 检查是否可以创建合作对接记录
 *     tags: [达人提报]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 提报记录ID
 *     responses:
 *       200:
 *         description: 检查完成
 *       404:
 *         description: 提报不存在
 */
router.get('/:id/can-create-cooperation', InfluencerReportController.canCreateCooperationRecord);

module.exports = router;
