const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const DateFormatter = require('../utils/dateFormatter');

/**
 * 达人公海模型（原爬虫结果模型）
 * 存储爬虫任务的具体结果数据，现在作为达人公海数据展示
 */
const PublicInfluencer = sequelize.define('PublicInfluencer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  taskId: {
    type: DataTypes.INTEGER,
    field: 'task_id',
    allowNull: false,
    comment: '关联的爬虫任务ID'
  },
  platform: {
    type: DataTypes.ENUM('xiaohongshu', 'juxingtu'),
    allowNull: false,
    comment: '平台类型'
  },
  platformUserId: {
    type: DataTypes.STRING(100),
    field: 'platform_user_id',
    allowNull: false,
    comment: '平台用户ID'
  },
  nickname: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '达人昵称'
  },
  avatarUrl: {
    type: DataTypes.STRING(500),
    field: 'avatar_url',
    comment: '头像链接'
  },
  followersCount: {
    type: DataTypes.INTEGER,
    field: 'followers_count',
    defaultValue: 0,
    comment: '粉丝数量'
  },
  city: {
    type: DataTypes.STRING(50),
    comment: '城市'
  },
  uniqueId: {
    type: DataTypes.STRING(100),
    field: 'unique_id',
    comment: '唯一标识'
  },
  contactInfo: {
    type: DataTypes.JSON,
    field: 'contact_info',
    comment: '联系方式'
  },
  videoStats: {
    type: DataTypes.JSON,
    field: 'video_stats',
    comment: '视频统计数据'
  },
  videoDetails: {
    type: DataTypes.JSON,
    field: 'video_details',
    comment: '视频详情数据'
  },
  rawData: {
    type: DataTypes.JSON,
    field: 'raw_data',
    comment: '原始爬取数据'
  },
  authorExtInfo: {
    type: DataTypes.JSON,
    field: 'author_ext_info',
    comment: '达人扩展信息（来自巨量星图列表接口的动态附加信息）'
  },
  contentTheme: {
    type: DataTypes.JSON,
    field: 'content_theme',
    comment: '内容主题（从authorExtInfo.content_theme_labels_180d提取）'
  },
  influencerTags: {
    type: DataTypes.JSON,
    field: 'influencer_tags',
    comment: '达人标签（从authorExtInfo.tags_relation提取）'
  },
  playMid: {
    type: DataTypes.STRING(50),
    field: 'play_mid',
    comment: '播放量中位数'
  },
  status: {
    type: DataTypes.ENUM('pending', 'processed', 'failed', 'imported', 'collected'),
    defaultValue: 'pending',
    comment: '处理状态：pending-待处理，processed-已处理，failed-失败，imported-已导入，collected-已收藏'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    field: 'error_message',
    comment: '错误信息'
  },
  importedInfluencerId: {
    type: DataTypes.INTEGER,
    field: 'imported_influencer_id',
    comment: '收藏到达人表的ID'
  }
}, {
  tableName: 'public_influencers',  // 新的表名
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['task_id']
    },
    {
      fields: ['platform']
    },
    {
      fields: ['platform_user_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['created_at']
    }
  ]
});

// 定义查询时排除的敏感字段
PublicInfluencer.QUERY_ATTRIBUTES = {
  exclude: ['rawData', 'errorMessage']
};

// 定义查询时包含的字段（排除敏感字段）
PublicInfluencer.SAFE_ATTRIBUTES = [
  'id', 'taskId', 'platform', 'platformUserId', 'nickname', 'avatarUrl',
  'followersCount', 'city', 'uniqueId', 'contactInfo', 'videoStats',
  'videoDetails', 'authorExtInfo', 'contentTheme', 'influencerTags',
  'playMid', 'status', 'importedInfluencerId', 'createdAt', 'updatedAt'
];

// 重写toJSON方法，自动格式化时间字段
PublicInfluencer.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return DateFormatter.formatObject(values);
};

module.exports = PublicInfluencer;
