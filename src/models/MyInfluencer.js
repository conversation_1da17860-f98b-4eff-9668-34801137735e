const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const DateFormatter = require('../utils/dateFormatter');

/**
 * 我的达人模型
 * 存储用户管理的达人信息
 */
const MyInfluencer = sequelize.define('MyInfluencer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  nickname: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '达人昵称'
  },
  platform: {
    type: DataTypes.ENUM('xiaohongshu', 'juxingtu'),
    allowNull: false,
    comment: '平台类型'
  },
  platformId: {
    type: DataTypes.STRING(100),
    field: 'platform_id',
    comment: '平台ID'
  },
  avatarUrl: {
    type: DataTypes.STRING(500),
    field: 'avatar_url',
    comment: '头像链接'
  },
  followersCount: {
    type: DataTypes.INTEGER,
    field: 'followers_count',
    defaultValue: 0,
    comment: '粉丝数量'
  },
  category: {
    type: DataTypes.STRING(50),
    comment: '分类'
  },
  tags: {
    type: DataTypes.JSON,
    comment: '标签'
  },
  contactInfo: {
    type: DataTypes.JSON,
    field: 'contact_info',
    comment: '联系方式'
  },
  priceInfo: {
    type: DataTypes.JSON,
    field: 'price_info',
    comment: '报价信息'
  },
  cooperationHistory: {
    type: DataTypes.JSON,
    field: 'cooperation_history',
    comment: '合作历史'
  },
  videoStats: {
    type: DataTypes.JSON,
    field: 'video_stats',
    comment: '视频统计数据（播放量、点赞数等）'
  },
  authorExtInfo: {
    type: DataTypes.JSON,
    field: 'author_ext_info',
    comment: '达人扩展信息（来自巨量星图列表接口的动态附加信息）'
  },
  contentTheme: {
    type: DataTypes.JSON,
    field: 'content_theme',
    comment: '内容主题（从authorExtInfo.content_theme_labels_180d提取）'
  },
  influencerTags: {
    type: DataTypes.JSON,
    field: 'influencer_tags',
    comment: '达人标签（从authorExtInfo.tags_relation提取）'
  },
  playMid: {
    type: DataTypes.STRING(50),
    field: 'play_mid',
    comment: '播放量中位数'
  },
  notes: {
    type: DataTypes.TEXT,
    comment: '备注'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active',
    comment: '状态'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    field: 'created_by',
    comment: '创建者ID'
  }
}, {
  tableName: 'my_influencers',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['platform']
    },
    {
      fields: ['nickname']
    },
    {
      fields: ['followers_count']
    },
    {
      fields: ['category']
    }
  ]
});

// 重写toJSON方法，自动格式化时间字段
MyInfluencer.prototype.toJSON = function() {
  const values = Object.assign({}, this.get());
  return DateFormatter.formatObject(values);
};

module.exports = MyInfluencer;
