/**
 * 批量处理脚本：检测和更新合作对接记录中的达人平台信息
 *
 * 功能：
 * 1. 扫描合作对接记录，筛选包含达人主页链接的记录
 * 2. 解析达人主页链接，提取平台类型和平台用户ID
 * 3. 生成对照文件和可执行的批量更新脚本
 * 4. 测试CRM客户信息更新接口
 *
 * 使用方法：
 * node scripts/batch-update-influencer-platform-info.js [options]
 *
 * 选项：
 * --dry-run: 仅检测和生成报告，不实际更新
 * --batch-size: 批处理大小，默认50
 * --test-only: 仅进行接口测试
 *
 * <AUTHOR>
 * @version 1.0.0
 */

const fs = require('fs');
const path = require('path');
const moment = require('moment');
const { CooperationManagement, User } = require('../src/models');
const CrmIntegrationService = require('../src/services/CrmIntegrationService');

class InfluencerPlatformInfoBatchProcessor {
  constructor(options = {}) {
    this.options = {
      dryRun: options.dryRun || false,
      batchSize: options.batchSize || 50,
      testOnly: options.testOnly || false,
      outputDir: options.outputDir || './batch-output'
    };

    this.results = {
      total: 0,
      processed: 0,
      success: 0,
      failed: 0,
      skipped: 0,
      errors: []
    };

    this.crmService = new CrmIntegrationService();
    this.timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
  }

  /**
   * 解析达人链接或ID，提取平台用户ID
   * @param {string} input - 用户输入的链接或ID
   * @returns {Object} 解析结果 { platform: string, platformUserId: string, error?: string }
   */
  parseInfluencerInput(input) {
    if (!input || typeof input !== 'string') {
      return { error: '请输入有效的达人链接或ID' };
    }

    const trimmedInput = input.trim();

    // 小红书蒲公英平台链接解析
    const xiaohongshuPatterns = [
      /https?:\/\/pgy\.xiaohongshu\.com\/solar\/pre-trade\/blogger-detail\/([a-zA-Z0-9]+)/,
      /https?:\/\/pgy\.xiaohongshu\.com\/solar\/cooperator\/blogger\/([a-zA-Z0-9]+)/,
      /https?:\/\/www\.xiaohongshu\.com\/user\/profile\/([a-zA-Z0-9]+)/
    ];

    for (const pattern of xiaohongshuPatterns) {
      const match = trimmedInput.match(pattern);
      if (match) {
        return {
          platform: 'xiaohongshu',
          platformUserId: match[1]
        };
      }
    }

    // 巨量星图平台链接解析
    const juxingtuPatterns = [
      /https?:\/\/www\.xingtu\.cn\/ad\/creator\/author-homepage\/douyin-video\/([0-9]+)/,
      /https?:\/\/www\.xingtu\.cn\/ad\/creator\/author-homepage\/([0-9]+)/
    ];

    for (const pattern of juxingtuPatterns) {
      const match = trimmedInput.match(pattern);
      if (match) {
        return {
          platform: 'juxingtu',
          platformUserId: match[1]
        };
      }
    }

    // 尝试直接解析为ID
    // 小红书ID通常是24位字符串（字母数字混合）
    if (/^[a-zA-Z0-9]{20,30}$/.test(trimmedInput)) {
      return {
        platform: 'xiaohongshu',
        platformUserId: trimmedInput
      };
    }

    // 巨量星图ID通常是纯数字
    if (/^[0-9]{8,20}$/.test(trimmedInput)) {
      return {
        platform: 'juxingtu',
        platformUserId: trimmedInput
      };
    }

    return {
      error: '无法识别的链接格式或ID，请检查输入内容'
    };
  }

  /**
   * 获取用户的CRM用户ID
   */
  async getUserCrmId(userId) {
    try {
      if (!userId) {
        return null;
      }

      const user = await User.findByPk(userId, {
        attributes: ['id', 'crmUserId', 'username']
      });

      if (user && user.crmUserId) {
        console.log(`👤 找到用户 ${user.username} 的CRM ID: ${user.crmUserId}`);
        return user.crmUserId;
      }

      console.log(`⚠️ 用户 ${userId} 没有CRM用户ID，将使用默认值`);
      return null;
    } catch (error) {
      console.error(`❌ 获取用户CRM ID失败:`, error.message);
      return null;
    }
  }

  /**
   * 获取包含达人主页链接的合作记录
   */
  async getCooperationRecordsWithInfluencerLinks() {
    try {
      console.log('🔍 开始扫描合作对接记录...');

      const records = await CooperationManagement.findAll({
        where: {
          influencerHomepage: {
            [require('sequelize').Op.and]: [
              { [require('sequelize').Op.ne]: null },
              { [require('sequelize').Op.ne]: '' }
            ]
          }
        },
        attributes: [
          'id',
          'customerName',
          'influencerHomepage',
          'externalCustomerId',
          'platform',
          'bloggerName',
          'createdBy',
          'createdAt'
        ],
        order: [['id', 'ASC']]
      });

      console.log(`📋 找到 ${records.length} 条包含达人主页链接的记录`);
      this.results.total = records.length;

      return records;
    } catch (error) {
      console.error('❌ 获取合作记录失败:', error.message);
      throw error;
    }
  }

  /**
   * 处理单条记录
   */
  async processRecord(record) {
    try {
      const parseResult = this.parseInfluencerInput(record.influencerHomepage);

      const result = {
        id: record.id,
        customerName: record.customerName,
        influencerHomepage: record.influencerHomepage,
        externalCustomerId: record.externalCustomerId,
        originalPlatform: record.platform,
        originalBloggerName: record.bloggerName,
        createdBy: record.createdBy,
        parseStatus: parseResult.error ? '失败' : '成功',
        parsedPlatform: parseResult.platform || '',
        parsedPlatformUserId: parseResult.platformUserId || '',
        parseError: parseResult.error || '',
        crmTestStatus: '',
        crmTestError: ''
      };

      if (!parseResult.error && this.options.testOnly) {
        // 测试CRM接口
        try {
          if (record.externalCustomerId) {
            const testResult = await this.testCrmUpdate(
              record.externalCustomerId,
              parseResult.platformUserId,
              record.createdBy
            );
            result.crmTestStatus = testResult.success ? '成功' : '失败';
            result.crmTestError = testResult.error || '';
          } else {
            result.crmTestStatus = '跳过';
            result.crmTestError = '无CRM客户ID';
          }
        } catch (error) {
          result.crmTestStatus = '异常';
          result.crmTestError = error.message;
        }
      }

      if (parseResult.error) {
        this.results.failed++;
      } else {
        this.results.success++;
      }

      this.results.processed++;
      return result;
    } catch (error) {
      console.error(`❌ 处理记录 ${record.id} 失败:`, error.message);
      this.results.failed++;
      this.results.errors.push({
        recordId: record.id,
        error: error.message
      });

      return {
        id: record.id,
        customerName: record.customerName,
        influencerHomepage: record.influencerHomepage,
        externalCustomerId: record.externalCustomerId,
        parseStatus: '异常',
        parseError: error.message
      };
    }
  }

  /**
   * 测试CRM更新接口
   */
  async testCrmUpdate(customerId, platformUserId, createdBy = null) {
    try {
      // 获取操作用户ID
      const operatorUserId = await this.getUserCrmId(createdBy);
      const effectiveUserId = operatorUserId || '0141435327853352'; // 使用默认值作为fallback

      // 更新达人平台用户ID到CRM系统
      const testData = {
        customer_textarea_13: platformUserId // 达人平台用户ID字段
      };

      console.log(
        `🧪 测试CRM更新 - 客户ID: ${customerId}, 平台用户ID: ${platformUserId}, 操作用户: ${effectiveUserId}`
      );

      // 调用真实接口进行测试
      const result = await this.crmService.updateCrmData('customer', customerId, testData, effectiveUserId);

      if (result.success) {
        return { success: true };
      } else {
        return { success: false, error: result.message || '更新失败' };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 生成对照文件
   */
  async generateComparisonFile(processedResults) {
    try {
      // 确保输出目录存在
      if (!fs.existsSync(this.options.outputDir)) {
        fs.mkdirSync(this.options.outputDir, { recursive: true });
      }

      const filename = `influencer-platform-comparison-${this.timestamp}.txt`;
      const filepath = path.join(this.options.outputDir, filename);

      let content = '# 达人平台信息对照表\n';
      content += `# 生成时间: ${moment().format('YYYY-MM-DD HH:mm:ss')}\n`;
      content += `# 总记录数: ${this.results.total}\n`;
      content += `# 处理成功: ${this.results.success}\n`;
      content += `# 处理失败: ${this.results.failed}\n`;
      content += `# 跳过记录: ${this.results.skipped}\n\n`;

      content +=
        '合作记录ID\t客户名称\t达人主页链接\t解析状态\t平台类型\t平台用户ID\tCRM客户ID\t原平台\t原博主名\t解析错误\n';
      content += ''.padEnd(150, '-') + '\n';

      processedResults.forEach(result => {
        content +=
          [
            result.id,
            result.customerName || '',
            result.influencerHomepage || '',
            result.parseStatus,
            result.parsedPlatform || '',
            result.parsedPlatformUserId || '',
            result.externalCustomerId || '',
            result.originalPlatform || '',
            result.originalBloggerName || '',
            result.parseError || ''
          ].join('\t') + '\n';
      });

      fs.writeFileSync(filepath, content, 'utf8');
      console.log(`📄 对照文件已生成: ${filepath}`);

      return filepath;
    } catch (error) {
      console.error('❌ 生成对照文件失败:', error.message);
      throw error;
    }
  }

  /**
   * 生成批量更新脚本
   */
  async generateUpdateScript(processedResults) {
    try {
      const successResults = processedResults.filter(
        r => r.parseStatus === '成功' && r.externalCustomerId && r.parsedPlatform
      );

      if (successResults.length === 0) {
        console.log('⚠️ 没有可更新的记录，跳过脚本生成');
        return null;
      }

      const filename = `batch-update-script-${this.timestamp}.js`;
      const filepath = path.join(this.options.outputDir, filename);

      let scriptContent = `/**
 * 批量更新达人平台信息脚本
 * 生成时间: ${moment().format('YYYY-MM-DD HH:mm:ss')}
 * 待更新记录数: ${successResults.length}
 */

const CrmIntegrationService = require('../src/services/CrmIntegrationService');
const { User } = require('../src/models');

async function batchUpdateInfluencerPlatformInfo() {
  const crmService = new CrmIntegrationService();
  const results = { success: 0, failed: 0, errors: [] };

  // 获取用户CRM ID的辅助函数
  async function getUserCrmId(userId) {
    try {
      if (!userId) return null;

      const user = await User.findByPk(userId, {
        attributes: ['id', 'crmUserId', 'username']
      });

      if (user && user.crmUserId) {
        console.log(\`👤 找到用户 \${user.username} 的CRM ID: \${user.crmUserId}\`);
        return user.crmUserId;
      }

      console.log(\`⚠️ 用户 \${userId} 没有CRM用户ID，将使用默认值\`);
      return null;
    } catch (error) {
      console.error(\`❌ 获取用户CRM ID失败:\`, error.message);
      return null;
    }
  }

  const updateTasks = [
`;

      successResults.forEach(result => {
        scriptContent += `    {
      cooperationId: ${result.id},
      customerId: '${result.externalCustomerId}',
      platform: '${result.parsedPlatform}',
      platformUserId: '${result.parsedPlatformUserId}',
      customerName: '${result.customerName}',
      createdBy: ${result.createdBy || 'null'}
    },
`;
      });

      scriptContent += `  ];

  console.log(\`🚀 开始批量更新 \${updateTasks.length} 条记录...\`);

  for (const task of updateTasks) {
    try {
      // 获取操作用户ID
      const operatorUserId = await getUserCrmId(task.createdBy);
      const effectiveUserId = operatorUserId || '0141435327853352'; // 使用默认值作为fallback

      // 更新达人平台用户ID到CRM系统
      const updateData = {
        customer_textarea_13: task.platformUserId // 达人平台用户ID字段
      };

      console.log(\`🔄 更新客户 \${task.customerName} (ID: \${task.customerId}) - 平台用户ID: \${task.platformUserId}, 操作用户: \${effectiveUserId}\`);

      const result = await crmService.updateCrmData('customer', task.customerId, updateData, effectiveUserId);
      
      if (result.success) {
        console.log(\`✅ 更新成功: \${task.customerName}\`);
        results.success++;
      } else {
        console.error(\`❌ 更新失败: \${task.customerName} - \${result.message}\`);
        results.failed++;
        results.errors.push({
          cooperationId: task.cooperationId,
          customerId: task.customerId,
          error: result.message
        });
      }

      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.error(\`❌ 更新异常: \${task.customerName} - \${error.message}\`);
      results.failed++;
      results.errors.push({
        cooperationId: task.cooperationId,
        customerId: task.customerId,
        error: error.message
      });
    }
  }

  console.log(\`📊 批量更新完成 - 成功: \${results.success}, 失败: \${results.failed}\`);
  
  if (results.errors.length > 0) {
    console.log('❌ 错误详情:');
    results.errors.forEach(err => {
      console.log(\`  - 合作记录ID: \${err.cooperationId}, 客户ID: \${err.customerId}, 错误: \${err.error}\`);
    });
  }

  return results;
}

// 执行脚本
if (require.main === module) {
  batchUpdateInfluencerPlatformInfo()
    .then(results => {
      console.log('🎯 脚本执行完成:', results);
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = batchUpdateInfluencerPlatformInfo;
`;

      fs.writeFileSync(filepath, scriptContent, 'utf8');
      console.log(`📜 批量更新脚本已生成: ${filepath}`);
      console.log(`📝 可执行的记录数: ${successResults.length}`);

      return filepath;
    } catch (error) {
      console.error('❌ 生成更新脚本失败:', error.message);
      throw error;
    }
  }

  /**
   * 生成执行报告
   */
  generateExecutionReport() {
    const report = {
      timestamp: this.timestamp,
      options: this.options,
      results: this.results,
      summary: {
        totalRecords: this.results.total,
        processedRecords: this.results.processed,
        successRate:
          this.results.total > 0 ? ((this.results.success / this.results.total) * 100).toFixed(2) + '%' : '0%',
        failureRate: this.results.total > 0 ? ((this.results.failed / this.results.total) * 100).toFixed(2) + '%' : '0%'
      }
    };

    console.log('\n📊 执行报告:');
    console.log(`   总记录数: ${report.summary.totalRecords}`);
    console.log(`   处理记录数: ${report.summary.processedRecords}`);
    console.log(`   成功率: ${report.summary.successRate}`);
    console.log(`   失败率: ${report.summary.failureRate}`);

    if (this.results.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. 记录ID: ${error.recordId}, 错误: ${error.error}`);
      });
    }

    return report;
  }

  /**
   * 用户确认提示
   */
  async getUserConfirmation(message) {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise(resolve => {
      rl.question(`${message} (y/N): `, answer => {
        rl.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });
  }

  /**
   * 主执行方法
   */
  async execute() {
    try {
      console.log('🚀 批量处理达人平台信息脚本启动');
      console.log(`📋 执行模式: ${this.options.dryRun ? '干运行（仅检测）' : '正式执行'}`);
      console.log(`📦 批处理大小: ${this.options.batchSize}`);
      console.log(`🧪 测试模式: ${this.options.testOnly ? '是' : '否'}`);

      // 安全确认
      if (!this.options.dryRun) {
        const confirmed = await this.getUserConfirmation(
          '⚠️  您即将执行正式的批量处理操作，这可能会修改数据库中的数据。确定要继续吗？'
        );
        if (!confirmed) {
          console.log('❌ 用户取消操作');
          return;
        }
      }

      // 获取需要处理的记录
      const records = await this.getCooperationRecordsWithInfluencerLinks();

      if (records.length === 0) {
        console.log('ℹ️  没有找到需要处理的记录');
        return;
      }

      // 分批处理记录
      const processedResults = [];
      const batches = Math.ceil(records.length / this.options.batchSize);

      for (let i = 0; i < batches; i++) {
        const start = i * this.options.batchSize;
        const end = Math.min(start + this.options.batchSize, records.length);
        const batch = records.slice(start, end);

        console.log(`\n📦 处理批次 ${i + 1}/${batches} (记录 ${start + 1}-${end})`);

        for (const record of batch) {
          const result = await this.processRecord(record);
          processedResults.push(result);

          // 显示进度
          const progress = ((this.results.processed / this.results.total) * 100).toFixed(1);
          console.log(`   ✓ 记录 ${record.id} 处理完成 (${progress}%)`);
        }

        // 批次间延迟
        if (i < batches - 1) {
          console.log('   ⏳ 批次间延迟 2 秒...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }

      // 生成输出文件
      console.log('\n📄 生成输出文件...');
      const comparisonFile = await this.generateComparisonFile(processedResults);

      if (!this.options.dryRun && !this.options.testOnly) {
        const scriptFile = await this.generateUpdateScript(processedResults);
        if (scriptFile) {
          console.log(`\n🔧 执行更新脚本命令:`);
          console.log(`   node ${scriptFile}`);
        }
      }

      // 生成执行报告
      const report = this.generateExecutionReport();

      console.log('\n🎯 批量处理完成！');
      console.log(`📁 输出目录: ${this.options.outputDir}`);

      return {
        success: true,
        report,
        files: {
          comparison: comparisonFile
        }
      };
    } catch (error) {
      console.error('💥 批量处理失败:', error.message);
      throw error;
    }
  }
}

// 命令行参数解析
function parseCommandLineArgs() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: false,
    batchSize: 50,
    testOnly: false,
    outputDir: './batch-output'
  };

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]) || 50;
        break;
      case '--test-only':
        options.testOnly = true;
        break;
      case '--output-dir':
        options.outputDir = args[++i] || './batch-output';
        break;
      case '--help':
        console.log(`
使用方法: node scripts/batch-update-influencer-platform-info.js [选项]

选项:
  --dry-run          仅检测和生成报告，不实际更新
  --batch-size N     批处理大小，默认50
  --test-only        仅进行接口测试
  --output-dir DIR   输出目录，默认./batch-output
  --help             显示帮助信息

示例:
  node scripts/batch-update-influencer-platform-info.js --dry-run
  node scripts/batch-update-influencer-platform-info.js --batch-size 20 --test-only
        `);
        process.exit(0);
    }
  }

  return options;
}

// 主执行入口
async function main() {
  try {
    const options = parseCommandLineArgs();
    const processor = new InfluencerPlatformInfoBatchProcessor(options);
    await processor.execute();
  } catch (error) {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = InfluencerPlatformInfoBatchProcessor;
