/**
 * 测试巨量星图getAuthorDetail方法功能
 * 
 * 使用方法：
 * node test_author_detail.js
 */

const axios = require('axios');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  // 测试用的达人ID（请替换为实际的达人ID）
  testAuthorId: '123456789', // 请替换为真实的巨量星图达人ID
  // 测试用的登录凭证（请替换为实际的token）
  authToken: 'your-auth-token-here' // 请替换为真实的认证token
};

/**
 * 测试获取达人详细信息
 */
async function testGetAuthorDetail() {
  console.log('🧪 开始测试巨量星图getAuthorDetail方法...\n');

  try {
    // 1. 测试获取达人详细信息
    console.log('📋 测试1: 获取达人详细信息');
    console.log(`   达人ID: ${TEST_CONFIG.testAuthorId}`);
    
    const response = await axios.post(
      `${TEST_CONFIG.baseUrl}/api/influencer-reports/smart/author-detail`,
      {
        platform: 'juxingtu',
        authorId: TEST_CONFIG.testAuthorId
      },
      {
        headers: {
          'Authorization': `Bearer ${TEST_CONFIG.authToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.data.success) {
      console.log('✅ 获取达人详细信息成功');
      console.log('📊 返回数据结构:');
      
      const data = response.data.data;
      console.log(`   - 平台: ${data.platform}`);
      console.log(`   - 达人ID: ${data.platformUserId}`);
      console.log(`   - 昵称: ${data.nickname}`);
      console.log(`   - 头像: ${data.avatarUrl ? '有' : '无'}`);
      console.log(`   - 粉丝数: ${data.followersCount}`);
      console.log(`   - 播放量中位数: ${data.playMid || '无'}`);
      console.log(`   - 城市: ${data.city}`);
      console.log(`   - 唯一ID: ${data.uniqueId}`);
      
      // 检查联系方式
      if (data.contactInfo) {
        console.log(`   - 联系方式:`);
        console.log(`     微信: ${data.contactInfo.wechat || '无'}`);
        console.log(`     电话: ${data.contactInfo.phone || '无'}`);
      }
      
      // 检查视频统计
      if (data.videoStats) {
        console.log(`   - 视频统计:`);
        console.log(`     视频数量: ${data.videoStats.videoCount || 0}`);
        console.log(`     平均播放量: ${data.videoStats.averagePlay || 0}`);
        console.log(`     总播放量: ${data.videoStats.totalPlay || 0}`);
        console.log(`     总点赞数: ${data.videoStats.totalLike || 0}`);
        console.log(`     总评论数: ${data.videoStats.totalComment || 0}`);
        console.log(`     总分享数: ${data.videoStats.totalShare || 0}`);
      }
      
      // 检查扩展信息
      if (data.authorExtInfo) {
        console.log(`   - 扩展信息:`);
        console.log(`     字段数量: ${Object.keys(data.authorExtInfo).length}`);
        console.log(`     提取时间: ${data.authorExtInfo._extractedAt}`);
        console.log(`     数据来源: ${data.authorExtInfo._source}`);
        console.log(`     版本: ${data.authorExtInfo._version}`);
        
        // 显示部分扩展字段
        const extFields = Object.keys(data.authorExtInfo).filter(key => !key.startsWith('_'));
        if (extFields.length > 0) {
          console.log(`     扩展字段: ${extFields.slice(0, 5).join(', ')}${extFields.length > 5 ? '...' : ''}`);
        }
      } else {
        console.log(`   - 扩展信息: 无`);
      }
      
      // 检查原始数据
      if (data.rawData) {
        console.log(`   - 原始数据: 有 (${Object.keys(data.rawData).length} 个字段)`);
      } else {
        console.log(`   - 原始数据: 无`);
      }
      
      console.log('\n✅ 测试通过：达人详细信息获取功能正常');
      
    } else {
      console.error('❌ 获取达人详细信息失败:', response.data.message);
      return false;
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.response) {
      console.error('   HTTP状态码:', error.response.status);
      console.error('   错误信息:', error.response.data?.message || error.response.data);
    }
    
    return false;
  }

  return true;
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n🧪 测试错误处理...\n');

  try {
    // 测试无效的达人ID
    console.log('📋 测试2: 无效达人ID处理');
    
    const response = await axios.post(
      `${TEST_CONFIG.baseUrl}/api/influencer-reports/smart/author-detail`,
      {
        platform: 'juxingtu',
        authorId: 'invalid-id-123'
      },
      {
        headers: {
          'Authorization': `Bearer ${TEST_CONFIG.authToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.data.success) {
      console.log('✅ 错误处理正常: 正确返回错误信息');
      console.log(`   错误信息: ${response.data.message}`);
    } else {
      console.log('⚠️ 意外成功: 无效ID应该返回错误');
    }

  } catch (error) {
    if (error.response && error.response.status >= 400) {
      console.log('✅ 错误处理正常: 正确返回HTTP错误状态');
      console.log(`   HTTP状态码: ${error.response.status}`);
      console.log(`   错误信息: ${error.response.data?.message || error.response.data}`);
    } else {
      console.error('❌ 意外错误:', error.message);
      return false;
    }
  }

  return true;
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 巨量星图getAuthorDetail方法功能测试');
  console.log('=' * 50);
  
  // 检查配置
  if (TEST_CONFIG.authToken === 'your-auth-token-here') {
    console.log('⚠️ 警告: 请先配置有效的认证token');
    console.log('   请修改 TEST_CONFIG.authToken 为实际的认证token');
  }
  
  if (TEST_CONFIG.testAuthorId === '123456789') {
    console.log('⚠️ 警告: 请先配置有效的达人ID');
    console.log('   请修改 TEST_CONFIG.testAuthorId 为实际的巨量星图达人ID');
  }
  
  console.log('');

  let allTestsPassed = true;

  // 运行测试
  const test1Result = await testGetAuthorDetail();
  allTestsPassed = allTestsPassed && test1Result;

  const test2Result = await testErrorHandling();
  allTestsPassed = allTestsPassed && test2Result;

  // 输出测试结果
  console.log('\n' + '=' * 50);
  if (allTestsPassed) {
    console.log('🎉 所有测试通过！getAuthorDetail方法功能正常');
  } else {
    console.log('❌ 部分测试失败，请检查实现');
  }
  console.log('=' * 50);
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testGetAuthorDetail,
  testErrorHandling,
  runTests
};
