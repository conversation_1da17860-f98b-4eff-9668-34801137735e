/**
 * 清理重复平台ID的脚本
 * 在我的达人库和公海达人库中，只保留每个平台ID的最新记录
 */

const { sequelize, MyInfluencer, PublicInfluencer } = require('./src/models');

async function cleanDuplicatePlatformIds() {
  const transaction = await sequelize.transaction();
  
  try {
    console.log('🧹 开始清理重复平台ID记录...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 1. 清理我的达人库重复记录
    console.log('\n📋 步骤1: 清理我的达人库重复记录');
    await cleanMyInfluencerDuplicates(transaction);

    // 2. 清理公海达人库重复记录
    console.log('\n📋 步骤2: 清理公海达人库重复记录');
    await cleanPublicInfluencerDuplicates(transaction);

    // 提交事务
    await transaction.commit();
    console.log('\n✅ 清理完成，所有更改已提交');

  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('❌ 清理失败，已回滚所有更改:', error.message);
    throw error;
  }
}

/**
 * 清理我的达人库重复记录
 */
async function cleanMyInfluencerDuplicates(transaction) {
  // 查找重复的平台ID记录
  const duplicateQuery = `
    SELECT platform, platform_id, COUNT(*) as count
    FROM my_influencers 
    WHERE platform_id IS NOT NULL AND platform_id != ''
    GROUP BY platform, platform_id 
    HAVING COUNT(*) > 1
    ORDER BY platform, platform_id
  `;

  const [duplicates] = await sequelize.query(duplicateQuery, { transaction });
  
  console.log(`🔍 发现 ${duplicates.length} 组重复的平台ID`);

  if (duplicates.length === 0) {
    console.log('✅ 我的达人库没有重复记录');
    return;
  }

  let totalDeleted = 0;

  for (const duplicate of duplicates) {
    const { platform, platform_id, count } = duplicate;
    console.log(`\n🔄 处理重复记录: ${platform} - ${platform_id} (${count}条记录)`);

    // 查找该平台ID的所有记录，按更新时间降序排列
    const records = await MyInfluencer.findAll({
      where: {
        platform: platform,
        platformId: platform_id
      },
      order: [['updatedAt', 'DESC'], ['createdAt', 'DESC'], ['id', 'DESC']],
      transaction
    });

    if (records.length <= 1) {
      console.log('⚠️ 跳过：实际记录数不足2条');
      continue;
    }

    // 保留最新的记录（第一条），删除其他记录
    const keepRecord = records[0];
    const deleteRecords = records.slice(1);

    console.log(`   保留记录: ID=${keepRecord.id}, 昵称=${keepRecord.nickname}, 更新时间=${keepRecord.updatedAt}`);
    
    for (const deleteRecord of deleteRecords) {
      console.log(`   删除记录: ID=${deleteRecord.id}, 昵称=${deleteRecord.nickname}, 更新时间=${deleteRecord.updatedAt}`);
      await deleteRecord.destroy({ transaction });
      totalDeleted++;
    }
  }

  console.log(`✅ 我的达人库清理完成，共删除 ${totalDeleted} 条重复记录`);
}

/**
 * 清理公海达人库重复记录
 */
async function cleanPublicInfluencerDuplicates(transaction) {
  // 查找重复的平台用户ID记录
  const duplicateQuery = `
    SELECT platform, platform_user_id, COUNT(*) as count
    FROM public_influencers 
    WHERE platform_user_id IS NOT NULL AND platform_user_id != ''
    GROUP BY platform, platform_user_id 
    HAVING COUNT(*) > 1
    ORDER BY platform, platform_user_id
  `;

  const [duplicates] = await sequelize.query(duplicateQuery, { transaction });
  
  console.log(`🔍 发现 ${duplicates.length} 组重复的平台用户ID`);

  if (duplicates.length === 0) {
    console.log('✅ 公海达人库没有重复记录');
    return;
  }

  let totalDeleted = 0;

  for (const duplicate of duplicates) {
    const { platform, platform_user_id, count } = duplicate;
    console.log(`\n🔄 处理重复记录: ${platform} - ${platform_user_id} (${count}条记录)`);

    // 查找该平台用户ID的所有记录，按更新时间降序排列
    const records = await PublicInfluencer.findAll({
      where: {
        platform: platform,
        platformUserId: platform_user_id
      },
      order: [['updatedAt', 'DESC'], ['createdAt', 'DESC'], ['id', 'DESC']],
      transaction
    });

    if (records.length <= 1) {
      console.log('⚠️ 跳过：实际记录数不足2条');
      continue;
    }

    // 保留最新的记录（第一条），删除其他记录
    const keepRecord = records[0];
    const deleteRecords = records.slice(1);

    console.log(`   保留记录: ID=${keepRecord.id}, 昵称=${keepRecord.nickname}, 更新时间=${keepRecord.updatedAt}`);
    
    for (const deleteRecord of deleteRecords) {
      console.log(`   删除记录: ID=${deleteRecord.id}, 昵称=${deleteRecord.nickname}, 更新时间=${deleteRecord.updatedAt}`);
      await deleteRecord.destroy({ transaction });
      totalDeleted++;
    }
  }

  console.log(`✅ 公海达人库清理完成，共删除 ${totalDeleted} 条重复记录`);
}

/**
 * 显示清理前的统计信息
 */
async function showStatsBefore() {
  console.log('\n📊 清理前统计信息:');
  
  // 我的达人库统计
  const myInfluencerTotal = await MyInfluencer.count();
  const myInfluencerWithPlatformId = await MyInfluencer.count({
    where: {
      platformId: { [require('sequelize').Op.ne]: null }
    }
  });
  
  console.log(`   我的达人库: 总计 ${myInfluencerTotal} 条，有平台ID ${myInfluencerWithPlatformId} 条`);
  
  // 公海达人库统计
  const publicInfluencerTotal = await PublicInfluencer.count();
  const publicInfluencerWithPlatformId = await PublicInfluencer.count({
    where: {
      platformUserId: { [require('sequelize').Op.ne]: null }
    }
  });
  
  console.log(`   公海达人库: 总计 ${publicInfluencerTotal} 条，有平台用户ID ${publicInfluencerWithPlatformId} 条`);
}

/**
 * 显示清理后的统计信息
 */
async function showStatsAfter() {
  console.log('\n📊 清理后统计信息:');
  
  // 我的达人库统计
  const myInfluencerTotal = await MyInfluencer.count();
  const myInfluencerWithPlatformId = await MyInfluencer.count({
    where: {
      platformId: { [require('sequelize').Op.ne]: null }
    }
  });
  
  console.log(`   我的达人库: 总计 ${myInfluencerTotal} 条，有平台ID ${myInfluencerWithPlatformId} 条`);
  
  // 公海达人库统计
  const publicInfluencerTotal = await PublicInfluencer.count();
  const publicInfluencerWithPlatformId = await PublicInfluencer.count({
    where: {
      platformUserId: { [require('sequelize').Op.ne]: null }
    }
  });
  
  console.log(`   公海达人库: 总计 ${publicInfluencerTotal} 条，有平台用户ID ${publicInfluencerWithPlatformId} 条`);
}

// 运行清理脚本
if (require.main === module) {
  (async () => {
    try {
      await showStatsBefore();
      await cleanDuplicatePlatformIds();

      // 重新连接数据库来显示最终统计
      await sequelize.authenticate();
      await showStatsAfter();
      await sequelize.close();

      console.log('🎉 清理脚本执行完成');
      process.exit(0);
    } catch (error) {
      console.error('💥 清理脚本执行失败:', error);
      process.exit(1);
    }
  })();
}

module.exports = cleanDuplicatePlatformIds;
