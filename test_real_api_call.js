/**
 * 真实API调用测试 - 验证minFirstNotePlayCount参数传递
 * 
 * 这个脚本直接调用后端API来测试参数传递
 */

const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000/api';

/**
 * 获取测试用的JWT token
 * 注意：这里需要先登录获取有效的token
 */
async function getTestToken() {
  try {
    // 使用测试账号登录
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin', // 替换为实际的测试账号
      password: 'admin123' // 替换为实际的测试密码
    });

    if (loginResponse.data.success) {
      return loginResponse.data.data.token;
    } else {
      throw new Error('登录失败');
    }
  } catch (error) {
    console.error('❌ 获取测试token失败:', error.message);
    console.log('💡 请确保：');
    console.log('   1. 后端服务正在运行 (http://localhost:3000)');
    console.log('   2. 数据库连接正常');
    console.log('   3. 测试账号存在且密码正确');
    return null;
  }
}

/**
 * 测试创建爬虫任务
 */
async function testCreateTaskWithMinPlayCount() {
  console.log('🧪 开始真实API测试...\n');

  try {
    // 获取测试token
    console.log('🔑 获取测试token...');
    const token = await getTestToken();
    if (!token) {
      console.log('❌ 无法获取测试token，跳过API测试');
      return;
    }
    console.log('✅ 测试token获取成功\n');

    // 准备测试数据
    const testData = {
      taskName: '测试minFirstNotePlayCount参数传递',
      platform: 'xiaohongshu',
      keywords: '美妆测试',
      maxPages: 1,
      priority: 1,
      config: {
        minFirstNotePlayCount: 15000, // 设置1.5万播放量阈值
        pageSize: 5,
        saveVideos: false
      }
    };

    console.log('📋 测试数据:');
    console.log(JSON.stringify(testData, null, 2));
    console.log('');

    // 发送创建任务请求
    console.log('📤 发送创建任务请求...');
    const response = await axios.post(`${API_BASE_URL}/crawler/tasks`, testData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ 任务创建成功!');
    console.log('📊 响应数据:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('');

    // 验证返回的任务数据
    const task = response.data.data;
    console.log('🔍 验证参数传递:');
    console.log('   任务ID:', task.id);
    console.log('   任务名称:', task.taskName);
    console.log('   平台:', task.platform);
    console.log('   config对象:', JSON.stringify(task.config, null, 2));
    console.log('   minFirstNotePlayCount:', task.config?.minFirstNotePlayCount);
    console.log('');

    if (task.config && task.config.minFirstNotePlayCount === 15000) {
      console.log('✅ minFirstNotePlayCount参数传递成功!');
    } else {
      console.log('❌ minFirstNotePlayCount参数传递失败!');
      console.log('   期望值: 15000');
      console.log('   实际值:', task.config?.minFirstNotePlayCount);
    }

    return task;

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    if (error.response) {
      console.error('   响应状态:', error.response.status);
      console.error('   响应数据:', JSON.stringify(error.response.data, null, 2));
    }
    return null;
  }
}

/**
 * 测试获取任务详情
 */
async function testGetTaskDetail(taskId, token) {
  console.log(`\n🔍 获取任务 ${taskId} 详情...\n`);

  try {
    const response = await axios.get(`${API_BASE_URL}/crawler/tasks/${taskId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ 任务详情获取成功!');
    const task = response.data.data;
    
    console.log('📋 任务详情:');
    console.log('   ID:', task.id);
    console.log('   名称:', task.taskName);
    console.log('   平台:', task.platform);
    console.log('   状态:', task.status);
    console.log('   配置:', JSON.stringify(task.config, null, 2));
    console.log('   minFirstNotePlayCount:', task.config?.minFirstNotePlayCount);
    console.log('');

    return task.config?.minFirstNotePlayCount;

  } catch (error) {
    console.error('❌ 获取任务详情失败:', error.message);
    return null;
  }
}

/**
 * 测试不同的参数值
 */
async function testDifferentValues() {
  console.log('🎯 测试不同的minFirstNotePlayCount值...\n');

  const token = await getTestToken();
  if (!token) {
    console.log('❌ 无法获取测试token，跳过测试');
    return;
  }

  const testValues = [1, 1000, 5000, 10000, 50000];
  const results = [];

  for (const value of testValues) {
    console.log(`📊 测试值: ${value}`);

    try {
      const testData = {
        taskName: `测试播放量${value}`,
        platform: 'xiaohongshu',
        keywords: '测试',
        maxPages: 1,
        priority: 0,
        config: {
          minFirstNotePlayCount: value,
          pageSize: 3,
          saveVideos: false
        }
      };

      const response = await axios.post(`${API_BASE_URL}/crawler/tasks`, testData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const task = response.data.data;
      const savedValue = task.config?.minFirstNotePlayCount;
      
      if (savedValue === value) {
        console.log(`   ✅ 值 ${value} 传递正确`);
        results.push({ value, status: 'success', savedValue });
      } else {
        console.log(`   ❌ 值传递错误: 期望 ${value}, 实际 ${savedValue}`);
        results.push({ value, status: 'failed', savedValue });
      }

    } catch (error) {
      console.log(`   ❌ 测试值 ${value} 失败: ${error.message}`);
      results.push({ value, status: 'error', error: error.message });
    }

    console.log('');
  }

  // 输出测试结果总结
  console.log('📊 测试结果总结:');
  results.forEach(result => {
    const status = result.status === 'success' ? '✅' : '❌';
    console.log(`   ${status} ${result.value}: ${result.status} ${result.savedValue !== undefined ? `(保存值: ${result.savedValue})` : ''}`);
  });
  console.log('');

  return results;
}

/**
 * 主测试函数
 */
async function runRealApiTest() {
  console.log('🚀 真实API调用测试 - minFirstNotePlayCount参数传递\n');
  console.log('=' * 60 + '\n');

  try {
    // 测试1: 基本功能测试
    const task = await testCreateTaskWithMinPlayCount();
    
    if (task) {
      // 获取token用于后续测试
      const token = await getTestToken();
      
      if (token) {
        // 测试2: 获取任务详情验证
        await testGetTaskDetail(task.id, token);
        
        console.log('-' * 40 + '\n');
        
        // 测试3: 不同值测试
        await testDifferentValues();
      }
    }

    console.log('🏁 真实API测试完成!\n');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

/**
 * 检查服务器状态
 */
async function checkServerStatus() {
  console.log('🔍 检查服务器状态...\n');

  try {
    const response = await axios.get(`${API_BASE_URL}/health`, {
      timeout: 5000
    });

    console.log('✅ 服务器运行正常');
    console.log('   状态:', response.status);
    console.log('   响应:', response.data);
    return true;

  } catch (error) {
    console.log('❌ 服务器连接失败');
    console.log('   错误:', error.message);
    console.log('');
    console.log('💡 请确保：');
    console.log('   1. 后端服务正在运行: npm run dev');
    console.log('   2. 服务器地址正确: http://localhost:3000');
    console.log('   3. 防火墙没有阻止连接');
    return false;
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  checkServerStatus().then(serverOk => {
    if (serverOk) {
      runRealApiTest().catch(console.error);
    } else {
      console.log('\n⚠️ 服务器不可用，跳过API测试');
      console.log('请先启动后端服务，然后重新运行测试');
    }
  });
}

module.exports = {
  getTestToken,
  testCreateTaskWithMinPlayCount,
  testGetTaskDetail,
  testDifferentValues,
  runRealApiTest,
  checkServerStatus
};
