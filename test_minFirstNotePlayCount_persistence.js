/**
 * 测试minFirstNotePlayCount参数持久性
 * 
 * 验证minFirstNotePlayCount参数在多次处理后是否会被重置为默认值1
 */

const XiaohongshuCrawler = require('./src/services/crawler/crawlers/XiaohongshuCrawler');

/**
 * 模拟爬虫配置
 */
function createTestConfig(minFirstNotePlayCount) {
  return {
    keywords: '测试',
    maxPages: 2,
    pageSize: 5,
    minFirstNotePlayCount: minFirstNotePlayCount,
    saveVideos: false
  };
}

/**
 * 测试不同的minFirstNotePlayCount值
 */
async function testMinFirstNotePlayCountPersistence() {
  console.log('🧪 测试minFirstNotePlayCount参数持久性\n');
  console.log('=' * 60 + '\n');

  const testValues = [
    0,      // 边界值：0
    1,      // 默认值：1
    1000,   // 常用值：1千
    5000,   // 常用值：5千
    10000,  // 常用值：1万
    50000,  // 常用值：5万
    undefined, // 未定义值
    null    // 空值
  ];

  for (const testValue of testValues) {
    console.log(`🎯 测试值: ${testValue} (类型: ${typeof testValue})`);
    
    try {
      // 创建爬虫实例
      const crawler = new XiaohongshuCrawler();
      
      // 创建测试配置
      const config = createTestConfig(testValue);
      console.log(`📋 原始配置: config.minFirstNotePlayCount = ${config.minFirstNotePlayCount}`);
      
      // 模拟crawl方法中的参数处理逻辑
      console.log('🔄 模拟crawl方法中的参数处理...');
      
      // 原始逻辑（有问题的）
      const originalLogic = config.minFirstNotePlayCount || 1;
      console.log(`   原始逻辑 (|| 1): ${originalLogic}`);
      
      // 修复后的逻辑
      const fixedLogic = config.minFirstNotePlayCount !== undefined ? config.minFirstNotePlayCount : 1;
      console.log(`   修复逻辑 (!== undefined): ${fixedLogic}`);
      
      // 检查是否有差异
      const hasDifference = originalLogic !== fixedLogic;
      if (hasDifference) {
        console.log(`   ⚠️ 发现差异! 原始逻辑会将 ${testValue} 改为 ${originalLogic}`);
      } else {
        console.log(`   ✅ 无差异，两种逻辑结果一致`);
      }
      
      // 模拟processAuthorsInBatches方法中的参数处理
      console.log('🔄 模拟processAuthorsInBatches方法中的参数处理...');
      
      const crawlOptions = {
        saveVideos: config.saveVideos !== false,
        crawlTaskId: config.crawlTaskId || null,
        minFirstNotePlayCount: fixedLogic // 使用修复后的逻辑
      };
      
      // 原始逻辑（有问题的）
      const batchOriginalLogic = crawlOptions.minFirstNotePlayCount || 1;
      console.log(`   批处理原始逻辑 (|| 1): ${batchOriginalLogic}`);
      
      // 修复后的逻辑
      const batchFixedLogic = crawlOptions.minFirstNotePlayCount !== undefined ? crawlOptions.minFirstNotePlayCount : 1;
      console.log(`   批处理修复逻辑 (!== undefined): ${batchFixedLogic}`);
      
      // 最终结果检查
      console.log('📊 最终结果:');
      console.log(`   输入值: ${testValue}`);
      console.log(`   最终值: ${batchFixedLogic}`);
      console.log(`   是否保持原值: ${testValue === batchFixedLogic ? '✅ 是' : '❌ 否'}`);
      
    } catch (error) {
      console.error(`❌ 测试值 ${testValue} 失败:`, error.message);
    }
    
    console.log('');
  }
}

/**
 * 测试边界情况
 */
async function testEdgeCases() {
  console.log('🔍 测试边界情况\n');
  console.log('-' * 40 + '\n');

  const edgeCases = [
    { name: '负数', value: -1 },
    { name: '小数', value: 1.5 },
    { name: '字符串数字', value: '1000' },
    { name: '字符串非数字', value: 'abc' },
    { name: '布尔值true', value: true },
    { name: '布尔值false', value: false },
    { name: '空对象', value: {} },
    { name: '空数组', value: [] }
  ];

  for (const testCase of edgeCases) {
    console.log(`🧪 测试 ${testCase.name}: ${testCase.value}`);
    
    // 原始逻辑
    const originalResult = testCase.value || 1;
    console.log(`   原始逻辑结果: ${originalResult} (类型: ${typeof originalResult})`);
    
    // 修复逻辑
    const fixedResult = testCase.value !== undefined ? testCase.value : 1;
    console.log(`   修复逻辑结果: ${fixedResult} (类型: ${typeof fixedResult})`);
    
    // 类型安全检查
    const isValidNumber = typeof fixedResult === 'number' && !isNaN(fixedResult) && fixedResult >= 0;
    console.log(`   是否为有效数字: ${isValidNumber ? '✅ 是' : '❌ 否'}`);
    
    if (!isValidNumber) {
      console.log(`   ⚠️ 建议: 需要额外的类型和范围验证`);
    }
    
    console.log('');
  }
}

/**
 * 测试实际使用场景
 */
async function testRealWorldScenarios() {
  console.log('🌍 测试实际使用场景\n');
  console.log('-' * 40 + '\n');

  const scenarios = [
    {
      name: '用户设置1万播放量',
      config: { minFirstNotePlayCount: 10000 },
      expected: 10000
    },
    {
      name: '用户设置0（表示不过滤）',
      config: { minFirstNotePlayCount: 0 },
      expected: 0
    },
    {
      name: '用户未设置（使用默认值）',
      config: {},
      expected: 1
    },
    {
      name: '用户明确设置为null',
      config: { minFirstNotePlayCount: null },
      expected: 1
    },
    {
      name: '配置对象为空',
      config: null,
      expected: 1
    }
  ];

  for (const scenario of scenarios) {
    console.log(`📋 场景: ${scenario.name}`);
    console.log(`   配置: ${JSON.stringify(scenario.config)}`);
    
    try {
      // 模拟完整的参数传递流程
      const config = scenario.config || {};
      
      // 第一步：crawl方法中的处理
      const crawlValue = config.minFirstNotePlayCount !== undefined ? config.minFirstNotePlayCount : 1;
      console.log(`   crawl方法处理后: ${crawlValue}`);
      
      // 第二步：processAuthorsInBatches方法中的处理
      const crawlOptions = { minFirstNotePlayCount: crawlValue };
      const finalValue = crawlOptions.minFirstNotePlayCount !== undefined ? crawlOptions.minFirstNotePlayCount : 1;
      console.log(`   最终使用值: ${finalValue}`);
      
      // 验证结果
      const isCorrect = finalValue === scenario.expected;
      console.log(`   期望值: ${scenario.expected}`);
      console.log(`   结果: ${isCorrect ? '✅ 正确' : '❌ 错误'}`);
      
      if (!isCorrect) {
        console.log(`   ⚠️ 期望 ${scenario.expected}，实际得到 ${finalValue}`);
      }
      
    } catch (error) {
      console.error(`   ❌ 场景测试失败:`, error.message);
    }
    
    console.log('');
  }
}

/**
 * 主测试函数
 */
async function runPersistenceTests() {
  console.log('🚀 minFirstNotePlayCount参数持久性测试\n');
  
  try {
    // 测试1: 基本持久性测试
    await testMinFirstNotePlayCountPersistence();
    
    console.log('=' * 60 + '\n');
    
    // 测试2: 边界情况测试
    await testEdgeCases();
    
    console.log('=' * 60 + '\n');
    
    // 测试3: 实际使用场景测试
    await testRealWorldScenarios();
    
    console.log('🏁 所有测试完成!\n');
    
    console.log('📋 修复总结:');
    console.log('   问题: 使用 || 1 会将0重置为1');
    console.log('   修复: 使用 !== undefined ? value : 1');
    console.log('   效果: 保持用户设置的0值，只有undefined时使用默认值');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runPersistenceTests().catch(console.error);
}

module.exports = {
  testMinFirstNotePlayCountPersistence,
  testEdgeCases,
  testRealWorldScenarios,
  runPersistenceTests
};
