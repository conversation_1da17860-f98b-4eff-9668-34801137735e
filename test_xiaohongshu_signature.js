/**
 * 小红书签名机制测试脚本
 * 
 * 使用方法：
 * node test_xiaohongshu_signature.js
 */

const XiaohongshuCrawler = require('./src/services/crawler/crawlers/XiaohongshuCrawler');
const XiaohongshuSigner = require('./src/services/crawler/crawlers/xiaohongshu_signer');

/**
 * 测试签名生成器
 */
async function testSigner() {
  console.log('🧪 测试1: 签名生成器基础功能');
  console.log('=' * 50);

  const signer = new XiaohongshuSigner();

  // 测试用例1: 帖子详情API
  const testCase1 = {
    apiPath: "/api/solar/note/64a1b2c3d4e5f6789abcdef0/detail",
    params: {
      bizCode: ""
    }
  };

  console.log('📋 测试用例1: 帖子详情API');
  console.log(`   API路径: ${testCase1.apiPath}`);
  console.log(`   参数:`, testCase1.params);

  const signature1 = signer.generateSignature(testCase1.apiPath, testCase1.params);
  console.log('✅ 签名生成成功:');
  console.log(`   X-s: ${signature1['X-s']}`);
  console.log(`   X-t: ${signature1['X-t']}`);

  // 测试用例2: 达人列表API
  const testCase2 = {
    apiPath: "/api/solar/cooperator/blogger/v2",
    params: {
      advertiseSwitch: 1,
      orderType: 1,
      pageNumber: 1,
      pageSize: 8,
      userId: "5ac63d1d4eacab4a4af08e12",
      noteType: 4,
      isThirdPlatform: 0
    }
  };

  console.log('\n📋 测试用例2: 达人列表API');
  console.log(`   API路径: ${testCase2.apiPath}`);
  console.log(`   参数:`, testCase2.params);

  const signature2 = signer.generateSignature(testCase2.apiPath, testCase2.params);
  console.log('✅ 签名生成成功:');
  console.log(`   X-s: ${signature2['X-s']}`);
  console.log(`   X-t: ${signature2['X-t']}`);

  // 测试X-S-Common生成
  console.log('\n📋 测试X-S-Common生成:');
  const xsCommon = signer.generateXSCommon();
  console.log(`   X-S-Common: ${xsCommon.substring(0, 100)}...`);

  console.log('\n✅ 签名生成器测试完成\n');
}

/**
 * 测试爬虫集成
 */
async function testCrawlerIntegration() {
  console.log('🧪 测试2: 爬虫签名集成');
  console.log('=' * 50);

  const crawler = new XiaohongshuCrawler();

  // 测试签名机制
  const testResult1 = crawler.testSignature(
    "/api/solar/note/64a1b2c3d4e5f6789abcdef0/detail",
    { bizCode: "" }
  );

  if (testResult1.success) {
    console.log('✅ 爬虫签名机制集成成功');
  } else {
    console.error('❌ 爬虫签名机制集成失败:', testResult1.error);
  }

  // 测试请求配置生成
  console.log('\n📋 测试请求配置生成:');
  
  try {
    // 模拟Cookie对象
    crawler.currentCookie = {
      cookieString: 'test_cookie=test_value',
      userAgent: 'Mozilla/5.0 (Test) AppleWebKit/537.36'
    };

    const config = crawler.createRequestConfig(
      'GET',
      'https://pgy.xiaohongshu.com/api/solar/note/test123/detail?bizCode=',
      null
    );

    console.log('✅ 请求配置生成成功:');
    console.log(`   方法: ${config.method}`);
    console.log(`   URL: ${config.url}`);
    console.log(`   X-s: ${config.headers['x-s'].substring(0, 20)}...`);
    console.log(`   X-t: ${config.headers['x-t']}`);
    console.log(`   X-S-Common: ${config.headers['X-S-Common'].substring(0, 50)}...`);
    console.log(`   Cookie: ${config.headers['Cookie']}`);

  } catch (error) {
    console.error('❌ 请求配置生成失败:', error.message);
  }

  console.log('\n✅ 爬虫集成测试完成\n');
}

/**
 * 测试不同API路径的签名
 */
async function testDifferentAPIs() {
  console.log('🧪 测试3: 不同API路径签名');
  console.log('=' * 50);

  const signer = new XiaohongshuSigner();

  const testCases = [
    {
      name: '帖子详情',
      apiPath: '/api/solar/note/64a1b2c3d4e5f6789abcdef0/detail',
      params: { bizCode: '' }
    },
    {
      name: '达人卡片',
      apiPath: '/api/solar/cooperator/user/blogger/5ac63d1d4eacab4a4af08e12',
      params: {}
    },
    {
      name: '达人笔记',
      apiPath: '/api/solar/kol/data_v2/notes_detail',
      params: {
        advertiseSwitch: 1,
        orderType: 1,
        pageNumber: 1,
        pageSize: 8,
        userId: '5ac63d1d4eacab4a4af08e12',
        noteType: 4,
        isThirdPlatform: 0
      }
    },
    {
      name: '达人列表',
      apiPath: '/api/solar/cooperator/blogger/v2',
      params: {
        keyword: '美妆',
        pageNum: 1,
        pageSize: 20
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📋 测试 ${testCase.name} API:`);
    console.log(`   路径: ${testCase.apiPath}`);
    console.log(`   参数:`, testCase.params);

    try {
      const signature = signer.generateSignature(testCase.apiPath, testCase.params);
      console.log(`   ✅ 签名: ${signature['X-s'].substring(0, 30)}...`);
      console.log(`   ✅ 时间戳: ${signature['X-t']}`);
    } catch (error) {
      console.error(`   ❌ 签名失败: ${error.message}`);
    }
  }

  console.log('\n✅ 不同API路径签名测试完成\n');
}

/**
 * 测试签名一致性
 */
async function testSignatureConsistency() {
  console.log('🧪 测试4: 签名一致性验证');
  console.log('=' * 50);

  const signer = new XiaohongshuSigner();
  const apiPath = '/api/solar/note/test/detail';
  const params = { bizCode: '' };
  const timestamp = Date.now();

  console.log('📋 使用固定时间戳生成多次签名，验证一致性:');
  console.log(`   时间戳: ${timestamp}`);

  const signatures = [];
  for (let i = 0; i < 3; i++) {
    const signature = signer.generateSignature(apiPath, params, timestamp);
    signatures.push(signature);
    console.log(`   第${i + 1}次: ${signature['X-s'].substring(0, 30)}...`);
  }

  // 验证一致性
  const allSame = signatures.every(sig => 
    sig['X-s'] === signatures[0]['X-s'] && 
    sig['X-t'] === signatures[0]['X-t']
  );

  if (allSame) {
    console.log('✅ 签名一致性验证通过');
  } else {
    console.error('❌ 签名一致性验证失败');
  }

  console.log('\n📋 测试不同时间戳的签名差异:');
  for (let i = 0; i < 3; i++) {
    const signature = signer.generateSignature(apiPath, params);
    console.log(`   签名${i + 1}: ${signature['X-s'].substring(0, 30)}... (${signature['X-t']})`);
    // 等待1毫秒确保时间戳不同
    await new Promise(resolve => setTimeout(resolve, 1));
  }

  console.log('\n✅ 签名一致性测试完成\n');
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 小红书签名机制集成测试');
  console.log('=' * 60);
  console.log('');

  try {
    await testSigner();
    await testCrawlerIntegration();
    await testDifferentAPIs();
    await testSignatureConsistency();

    console.log('🎉 所有测试完成！');
    console.log('✅ 小红书签名机制已成功集成到爬虫系统中');
    console.log('');
    console.log('📋 集成功能说明:');
    console.log('   - ✅ 动态签名生成 (X-s, X-t)');
    console.log('   - ✅ X-S-Common头生成');
    console.log('   - ✅ 与Cookie管理系统兼容');
    console.log('   - ✅ 支持GET和POST请求');
    console.log('   - ✅ 自动参数提取和签名');
    console.log('   - ✅ 错误处理和日志记录');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error(error.stack);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testSigner,
  testCrawlerIntegration,
  testDifferentAPIs,
  testSignatureConsistency,
  runTests
};
