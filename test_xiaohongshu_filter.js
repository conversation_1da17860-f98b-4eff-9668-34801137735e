/**
 * 小红书爬虫首个帖子播放量过滤功能测试
 * 
 * 测试新增的minFirstNotePlayCount过滤功能
 */

const XiaohongshuCrawler = require('./src/services/crawler/crawlers/XiaohongshuCrawler');

async function testFirstNotePlayCountFilter() {
  console.log('🧪 开始测试小红书爬虫首个帖子播放量过滤功能...\n');

  try {
    // 创建爬虫实例
    const crawler = new XiaohongshuCrawler();
    
    // 初始化爬虫
    await crawler.initialize();
    console.log('✅ 爬虫初始化成功\n');

    // 测试配置
    const testConfig = {
      keywords: '美妆',
      maxPages: 1,
      pageSize: 5,
      minFirstNotePlayCount: 10000, // 设置较高的阈值进行测试
      saveVideos: false // 测试时不保存视频数据
    };

    console.log('📋 测试配置:');
    console.log(`   关键词: ${testConfig.keywords}`);
    console.log(`   最大页数: ${testConfig.maxPages}`);
    console.log(`   每页数量: ${testConfig.pageSize}`);
    console.log(`   首个帖子最低播放量: ${testConfig.minFirstNotePlayCount}`);
    console.log('');

    // 定义回调函数
    const callbacks = {
      onProgress: async (progress) => {
        console.log(`📊 进度更新: ${progress.percentage}% (${progress.currentPage}/${progress.totalPages})`);
        console.log(`   成功: ${progress.successCount}, 失败: ${progress.failedCount}`);
      },
      onResult: async (result) => {
        console.log(`🎉 获取到达人: ${result.nickname} (${result.platformUserId})`);
      },
      onError: async (error) => {
        console.log(`❌ 错误: ${error.message}`);
      }
    };

    // 执行爬取测试
    console.log('🚀 开始执行爬取测试...\n');
    const results = await crawler.crawl(testConfig, callbacks);

    // 输出测试结果
    console.log('\n📊 测试结果统计:');
    console.log(`   总计达人数: ${results.totalCount}`);
    console.log(`   成功处理: ${results.successCount}`);
    console.log(`   失败/跳过: ${results.failedCount}`);
    console.log(`   实际获取数据: ${results.data.length}`);

    if (results.data.length > 0) {
      console.log('\n✅ 通过过滤的达人列表:');
      results.data.forEach((author, index) => {
        console.log(`   ${index + 1}. ${author.nickname} (${author.platformUserId})`);
        console.log(`      粉丝数: ${author.followersCount}`);
        console.log(`      城市: ${author.city}`);
      });
    } else {
      console.log('\n⚠️ 没有达人通过首个帖子播放量过滤条件');
    }

    console.log('\n🎯 过滤功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 单独测试首个帖子播放量检查方法
async function testCheckFirstNotePlayCount() {
  console.log('\n🔍 测试首个帖子播放量检查方法...\n');

  try {
    const crawler = new XiaohongshuCrawler();
    await crawler.initialize();

    // 测试用的达人ID（需要替换为实际的达人ID）
    const testAuthorId = '5f8a9b2c3d4e5f6789012345'; // 示例ID，实际使用时需要替换
    const testMinPlayCount = 5000;

    console.log(`📋 测试参数:`);
    console.log(`   达人ID: ${testAuthorId}`);
    console.log(`   最低播放量要求: ${testMinPlayCount}`);
    console.log('');

    const checkResult = await crawler.checkFirstNotePlayCount(testAuthorId, testMinPlayCount);

    console.log('📊 检查结果:');
    console.log(`   是否通过: ${checkResult.passed ? '✅ 是' : '❌ 否'}`);
    console.log(`   首个帖子播放量: ${checkResult.firstNotePlayCount}`);
    console.log(`   帖子ID: ${checkResult.noteId || '无'}`);
    console.log(`   原因: ${checkResult.reason}`);

  } catch (error) {
    console.error('❌ 单独测试失败:', error.message);
  }
}

// 运行测试
async function runTests() {
  console.log('🎯 小红书爬虫首个帖子播放量过滤功能测试\n');
  console.log('=' * 60);
  
  // 测试1: 完整的爬取流程测试
  await testFirstNotePlayCountFilter();
  
  console.log('\n' + '=' * 60);
  
  // 测试2: 单独的检查方法测试
  await testCheckFirstNotePlayCount();
  
  console.log('\n🏁 所有测试完成！');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testFirstNotePlayCountFilter,
  testCheckFirstNotePlayCount,
  runTests
};
