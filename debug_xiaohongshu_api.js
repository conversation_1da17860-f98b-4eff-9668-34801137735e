/**
 * 调试小红书API响应的脚本
 */

const XiaohongshuCrawler = require('./src/services/crawler/crawlers/XiaohongshuCrawler');

async function debugXiaohongshuAPI() {
  console.log('🔍 开始调试小红书API...\n');
  
  try {
    // 创建爬虫实例
    const crawler = new XiaohongshuCrawler();
    
    // 刷新Cookie
    console.log('🍪 刷新Cookie...');
    await crawler.refreshCookie();
    
    // 测试达人ID
    const testAuthorId = '6304b0aa000000001200e019';
    console.log(`👤 测试达人ID: ${testAuthorId}\n`);
    
    // 调用API
    console.log('📡 调用 getAuthorPageInfo...');
    const result = await crawler.getAuthorPageInfo(testAuthorId);
    
    console.log('\n📊 返回结果:');
    console.log('=' .repeat(60));
    console.log(JSON.stringify(result, null, 2));
    console.log('=' .repeat(60));
    
    // 分析结果
    if (result.pagePercentVo) {
      console.log('\n✅ 找到 pagePercentVo 数据');
      console.log('📋 可用字段:', Object.keys(result.pagePercentVo));
      
      // 检查关键字段
      const keyFields = ['nickname', 'fansCount', 'noteCount', 'avgReadCount'];
      keyFields.forEach(field => {
        const value = result.pagePercentVo[field];
        console.log(`   ${field}: ${value !== undefined ? value : '❌ 不存在'}`);
      });
    } else {
      console.log('\n❌ 没有找到 pagePercentVo 数据');
      
      if (result.rawResponse) {
        console.log('📋 原始响应中的字段:', Object.keys(result.rawResponse));
      }
    }
    
  } catch (error) {
    console.error('\n❌ 调试过程中发生错误:', error.message);
    console.error('📋 错误详情:', error);
  }
}

// 运行调试
if (require.main === module) {
  debugXiaohongshuAPI();
}
