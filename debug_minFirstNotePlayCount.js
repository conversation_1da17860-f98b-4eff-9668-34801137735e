/**
 * 调试minFirstNotePlayCount参数传递问题
 * 
 * 这个脚本用于快速验证参数是否正确传递到各个环节
 */

// 模拟前端数据
function simulateFrontendData() {
  console.log('🎭 模拟前端数据构建...\n');

  // 模拟用户在前端表单中输入的数据
  const form = {
    id: '',
    taskName: '测试首个帖子播放量过滤',
    platform: 'xiaohongshu',
    keywords: '美妆测试',
    maxPages: 2,
    priority: 1,
    minFirstNotePlayCount: 10000, // 用户设置的播放量阈值
    config: {
      pageSize: 10,
      saveVideos: false
    }
  };

  console.log('📝 前端form对象:');
  console.log(JSON.stringify(form, null, 2));
  console.log('');

  // 模拟handleSubmit方法的数据处理
  const submitData = {
    ...form,
    config: {
      ...form.config,
      minFirstNotePlayCount: form.minFirstNotePlayCount || 1
    }
  };

  console.log('📤 提交到后端的数据 (submitData):');
  console.log(JSON.stringify(submitData, null, 2));
  console.log('');

  return submitData;
}

// 模拟后端控制器接收数据
function simulateControllerReceive(requestBody) {
  console.log('🎯 模拟后端控制器接收数据...\n');

  const { taskName, platform, keywords, config, priority, maxPages } = requestBody;

  console.log('📥 控制器解构的数据:');
  console.log('   taskName:', taskName);
  console.log('   platform:', platform);
  console.log('   keywords:', keywords);
  console.log('   config:', JSON.stringify(config, null, 2));
  console.log('   priority:', priority);
  console.log('   maxPages:', maxPages);
  console.log('   minFirstNotePlayCount from config:', config?.minFirstNotePlayCount);
  console.log('');

  // 模拟传递给crawlerService.createTask的数据
  const taskData = {
    taskName,
    platform,
    keywords,
    config: config || {},
    priority: priority || 0,
    maxPages: maxPages || 5,
    createdBy: 1 // 模拟用户ID
  };

  console.log('📤 传递给crawlerService的数据:');
  console.log(JSON.stringify(taskData, null, 2));
  console.log('');

  return taskData;
}

// 模拟数据库保存后的任务对象
function simulateTaskFromDatabase(taskData) {
  console.log('💾 模拟数据库保存的任务对象...\n');

  const task = {
    id: 123,
    taskName: taskData.taskName,
    platform: taskData.platform,
    keywords: taskData.keywords,
    config: taskData.config, // 这里应该包含minFirstNotePlayCount
    priority: taskData.priority,
    maxPages: taskData.maxPages,
    createdBy: taskData.createdBy,
    status: 'pending',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  console.log('📋 数据库中的任务对象:');
  console.log(JSON.stringify(task, null, 2));
  console.log('');
  console.log('🔍 检查config中的minFirstNotePlayCount:');
  console.log('   task.config?.minFirstNotePlayCount:', task.config?.minFirstNotePlayCount);
  console.log('');

  return task;
}

// 模拟CrawlerManager构建爬取配置
function simulateCrawlerManagerConfig(task) {
  console.log('⚙️ 模拟CrawlerManager构建爬取配置...\n');

  console.log('📥 CrawlerManager接收到的任务:');
  console.log('   任务ID:', task.id);
  console.log('   任务名称:', task.taskName);
  console.log('   平台:', task.platform);
  console.log('   原始config:', JSON.stringify(task.config, null, 2));
  console.log('   minFirstNotePlayCount from config:', task.config?.minFirstNotePlayCount);
  console.log('');

  // 构建爬取配置
  const crawlConfig = {
    keywords: task.keywords,
    maxPages: task.maxPages || 5,
    pageSize: task.config?.pageSize || 20,
    delay: task.config?.delay || { min: 1000, max: 3000 },
    retries: task.config?.retries || 3,
    filters: task.config?.filters || {},
    crawlTaskId: task.id,
    saveVideos: task.config?.saveVideos !== false,
    startPage: task.currentPage || 1,
    minFirstNotePlayCount: task.config?.minFirstNotePlayCount || 1 // 关键参数
  };

  console.log('📤 最终的爬取配置 (crawlConfig):');
  console.log(JSON.stringify(crawlConfig, null, 2));
  console.log('');
  console.log('🎯 关键检查 - minFirstNotePlayCount:');
  console.log('   原始值:', task.config?.minFirstNotePlayCount);
  console.log('   最终值:', crawlConfig.minFirstNotePlayCount);
  console.log('   是否正确传递:', crawlConfig.minFirstNotePlayCount === task.config?.minFirstNotePlayCount);
  console.log('');

  return crawlConfig;
}

// 模拟爬虫接收配置
function simulateCrawlerReceiveConfig(crawlConfig) {
  console.log('🕷️ 模拟爬虫接收配置...\n');

  console.log('📥 爬虫接收到的配置:');
  console.log('   minFirstNotePlayCount:', crawlConfig.minFirstNotePlayCount);
  console.log('');

  // 模拟processAuthorsInBatches方法中的参数传递
  const crawlOptions = {
    saveVideos: crawlConfig.saveVideos !== false,
    crawlTaskId: crawlConfig.crawlTaskId || null,
    minFirstNotePlayCount: crawlConfig.minFirstNotePlayCount || 1
  };

  console.log('📤 传递给processAuthorsInBatches的选项:');
  console.log(JSON.stringify(crawlOptions, null, 2));
  console.log('');

  // 模拟在processAuthorsInBatches中的使用
  const minFirstNotePlayCount = crawlOptions.minFirstNotePlayCount || 1;
  console.log('🎯 在processAuthorsInBatches中使用的阈值:', minFirstNotePlayCount);
  console.log('');

  return crawlOptions;
}

// 检查数据流的完整性
function checkDataFlowIntegrity(originalValue, finalValue) {
  console.log('🔍 数据流完整性检查...\n');

  console.log('📊 数据传递结果:');
  console.log('   原始值 (前端输入):', originalValue);
  console.log('   最终值 (爬虫使用):', finalValue);
  console.log('   传递是否成功:', originalValue === finalValue ? '✅ 成功' : '❌ 失败');
  console.log('');

  if (originalValue !== finalValue) {
    console.log('❌ 数据传递失败，可能的原因:');
    console.log('   1. 前端handleSubmit方法数据构建错误');
    console.log('   2. 后端控制器参数解构错误');
    console.log('   3. 数据库保存时config字段处理错误');
    console.log('   4. CrawlerManager配置构建时参数遗漏');
    console.log('   5. 爬虫接收配置时路径错误');
    console.log('');
  } else {
    console.log('✅ 数据传递成功！所有环节都正确处理了minFirstNotePlayCount参数');
    console.log('');
  }
}

// 主测试函数
function runDebugTest() {
  console.log('🚀 minFirstNotePlayCount参数传递调试测试\n');
  console.log('=' * 60 + '\n');

  // 1. 模拟前端数据
  const frontendData = simulateFrontendData();
  const originalValue = frontendData.config.minFirstNotePlayCount;

  console.log('-' * 40 + '\n');

  // 2. 模拟后端控制器接收
  const taskData = simulateControllerReceive(frontendData);

  console.log('-' * 40 + '\n');

  // 3. 模拟数据库保存
  const task = simulateTaskFromDatabase(taskData);

  console.log('-' * 40 + '\n');

  // 4. 模拟CrawlerManager配置构建
  const crawlConfig = simulateCrawlerManagerConfig(task);

  console.log('-' * 40 + '\n');

  // 5. 模拟爬虫接收配置
  const crawlOptions = simulateCrawlerReceiveConfig(crawlConfig);
  const finalValue = crawlOptions.minFirstNotePlayCount;

  console.log('-' * 40 + '\n');

  // 6. 检查数据流完整性
  checkDataFlowIntegrity(originalValue, finalValue);

  console.log('🏁 调试测试完成!\n');

  // 输出关键路径总结
  console.log('📋 关键数据路径总结:');
  console.log(`   前端输入: ${originalValue}`);
  console.log(`   提交数据: ${frontendData.config.minFirstNotePlayCount}`);
  console.log(`   控制器接收: ${taskData.config.minFirstNotePlayCount}`);
  console.log(`   数据库保存: ${task.config.minFirstNotePlayCount}`);
  console.log(`   爬取配置: ${crawlConfig.minFirstNotePlayCount}`);
  console.log(`   爬虫使用: ${finalValue}`);
  console.log('');
}

// 如果直接运行此文件，则执行调试测试
if (require.main === module) {
  runDebugTest();
}

module.exports = {
  simulateFrontendData,
  simulateControllerReceive,
  simulateTaskFromDatabase,
  simulateCrawlerManagerConfig,
  simulateCrawlerReceiveConfig,
  checkDataFlowIntegrity,
  runDebugTest
};
