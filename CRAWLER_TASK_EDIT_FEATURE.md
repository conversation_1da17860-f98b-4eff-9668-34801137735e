# 爬虫任务编辑功能

## 🎯 功能概述

为爬虫管理系统添加了完整的任务编辑功能，用户可以修改未启动、已暂停或已取消的爬虫任务的配置信息。

## ✨ 功能特性

### 1. 编辑按钮
- **位置**: 任务列表的操作列
- **显示条件**: 任务状态为 `pending`、`paused`、`cancelled` 时显示
- **样式**: 文本链接，与其他操作按钮保持一致

### 2. 编辑模式
- **弹窗标题**: 自动切换为"编辑任务"
- **表单预填充**: 自动填入当前任务的所有配置信息
- **提交按钮**: 显示"更新"而非"创建"

### 3. 可编辑字段
- **任务名称** (taskName)
- **平台类型** (platform) 
- **搜索关键词** (keywords)
- **最大页数** (maxPages)
- **任务优先级** (priority)
- **首个帖子最低播放量** (minFirstNotePlayCount)
- **配置参数** (config) - JSON格式

### 4. 状态限制
只允许编辑以下状态的任务：
- `pending` - 待执行
- `paused` - 已暂停
- `cancelled` - 已取消
- `failed` - 执行失败

不允许编辑的状态：
- `running` - 正在执行
- `completed` - 已完成

## 🔧 技术实现

### 前端实现

#### 1. 新增状态变量
```javascript
// 编辑模式状态
const isEditMode = ref(false);
const editingTaskId = ref(null);
```

#### 2. 编辑方法
```javascript
// 编辑任务
const editTask = (task) => {
  isEditMode.value = true;
  editingTaskId.value = task.id;
  
  // 填充表单数据
  form.taskName = task.taskName;
  form.platform = task.platform;
  form.keywords = task.keywords;
  form.maxPages = task.maxPages;
  form.priority = task.priority;
  form.minFirstNotePlayCount = task.config?.minFirstNotePlayCount || 1;
  form.config = { ...task.config } || {};
  
  modalVisible.value = true;
};
```

#### 3. 提交逻辑修改
```javascript
const handleSubmit = async () => {
  // ... 表单验证
  
  let response;
  if (isEditMode.value) {
    // 编辑模式：调用更新API
    response = await crawlerAPI.update(editingTaskId.value, submitData);
    if (response.success) {
      Message.success('更新成功');
      // 重置编辑状态
      isEditMode.value = false;
      editingTaskId.value = null;
    }
  } else {
    // 创建模式：调用创建API
    response = await crawlerAPI.create(submitData);
  }
};
```

#### 4. API服务扩展
```javascript
// 新增更新任务API
export const crawlerAPI = {
  // ... 其他方法
  update: (id, data) => api.put(`/crawler/tasks/${id}`, data),
};
```

### 后端实现

#### 1. 控制器方法
```javascript
/**
 * 更新爬虫任务
 */
async updateTask(ctx) {
  try {
    const { id } = ctx.params;
    const { taskName, platform, keywords, maxPages, priority, config } = ctx.request.body;

    // 验证任务是否存在
    const existingTask = await CrawlTask.findByPk(id);
    if (!existingTask) {
      ctx.throw(404, '任务不存在');
    }

    // 检查任务状态
    const allowedStatuses = ['pending', 'paused', 'cancelled', 'failed'];
    if (!allowedStatuses.includes(existingTask.status)) {
      ctx.throw(400, `任务状态为 ${existingTask.status}，无法编辑`);
    }

    // 构建更新数据
    const updateData = {};
    if (taskName !== undefined) updateData.taskName = taskName;
    if (platform !== undefined) updateData.platform = platform;
    if (keywords !== undefined) updateData.keywords = keywords;
    if (maxPages !== undefined) updateData.maxPages = maxPages;
    if (priority !== undefined) updateData.priority = priority;
    if (config !== undefined) updateData.config = config;

    // 更新任务
    await existingTask.update(updateData);

    ctx.body = {
      success: true,
      message: '任务更新成功',
      data: await CrawlTask.findByPk(id)
    };
  } catch (error) {
    console.error('更新爬虫任务失败:', error);
    ctx.throw(error.status || 400, error.message);
  }
}
```

#### 2. 路由配置
```javascript
// 添加更新任务路由
router.put('/tasks/:id', CrawlerController.updateTask);
```

#### 3. Swagger文档
完整的API文档，包括：
- 请求参数说明
- 响应格式定义
- 错误状态码说明
- 示例数据

## 📱 使用流程

### 1. 编辑任务
1. 在任务列表中找到要编辑的任务
2. 确认任务状态允许编辑（pending/paused/cancelled/failed）
3. 点击"编辑"按钮
4. 在弹出的表单中修改需要的字段
5. 点击"确定"提交更新

### 2. 表单预填充
- 所有字段自动填入当前任务的配置
- 包括JSON格式的配置参数
- 用户可以选择性修改需要的字段

### 3. 验证和提交
- 表单验证确保数据有效性
- 后端验证任务状态和权限
- 成功更新后刷新任务列表

## 🔒 安全和限制

### 1. 状态检查
- 前端：根据任务状态显示/隐藏编辑按钮
- 后端：严格验证任务状态，拒绝不允许的编辑

### 2. 权限控制
- 需要用户认证
- 只能编辑自己创建的任务（如果有用户权限系统）

### 3. 数据验证
- 前端表单验证
- 后端参数验证
- 数据类型和范围检查

## 🧪 测试场景

### 1. 正常编辑流程
- [ ] 创建一个pending状态的任务
- [ ] 点击编辑按钮
- [ ] 修改任务名称和配置
- [ ] 提交更新
- [ ] 验证任务信息已更新

### 2. 状态限制测试
- [ ] 启动一个任务（running状态）
- [ ] 确认编辑按钮不显示
- [ ] 直接调用API尝试编辑
- [ ] 验证返回错误信息

### 3. 表单验证测试
- [ ] 输入无效的JSON配置
- [ ] 输入超出范围的数值
- [ ] 提交空的必填字段
- [ ] 验证错误提示正确显示

### 4. 配置参数测试
- [ ] 编辑minFirstNotePlayCount参数
- [ ] 修改复杂的JSON配置
- [ ] 验证配置正确保存和应用

## 🎯 业务价值

### 1. 用户体验提升
- 无需重新创建任务
- 快速调整任务配置
- 减少操作步骤

### 2. 灵活性增强
- 支持任务配置的动态调整
- 适应业务需求变化
- 提高任务管理效率

### 3. 错误修正
- 快速修正配置错误
- 避免重复创建任务
- 保持任务历史记录

## 🔮 后续优化

### 1. 批量编辑
- 支持选择多个任务进行批量编辑
- 批量修改优先级或配置

### 2. 编辑历史
- 记录任务编辑历史
- 支持配置版本回滚

### 3. 权限细化
- 不同用户的编辑权限控制
- 管理员可编辑所有任务

### 4. 实时验证
- 配置参数的实时验证
- 智能提示和建议

## ✅ 总结

爬虫任务编辑功能为系统增加了重要的灵活性，用户可以：
- ✅ 编辑未启动、已暂停或失败的任务
- ✅ 修改所有任务配置参数
- ✅ 享受完整的表单验证和错误处理
- ✅ 通过直观的界面进行操作

这个功能大大提升了任务管理的便利性和用户体验，是爬虫系统的重要改进。

---

**功能版本**: v1.6.0  
**开发时间**: 2024年  
**开发人员**: 达人管理系统开发团队
