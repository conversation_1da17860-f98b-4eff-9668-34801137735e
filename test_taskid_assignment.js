/**
 * 测试更新现有达人时任务ID赋值功能
 * 
 * 验证在更新现有达人记录时，是否正确将当前爬虫任务ID赋值给达人记录
 */

const { PublicInfluencer } = require('./src/models');
const CrawlerManager = require('./src/services/crawler/CrawlerManager');

/**
 * 创建测试达人数据
 */
function createTestInfluencerData(taskId, platformUserId) {
  return {
    taskId: taskId,
    platform: 'xiaohongshu',
    platformUserId: platformUserId,
    nickname: '测试达人',
    avatarUrl: 'https://example.com/avatar.jpg',
    followersCount: 10000,
    city: '北京',
    uniqueId: 'test_unique_id',
    contactInfo: null,
    videoStats: {
      videoCount: 100,
      totalPlay: 1000000,
      averagePlay: 10000
    },
    videoDetails: [],
    rawData: {},
    authorExtInfo: {},
    contentTheme: null,
    influencerTags: null,
    playMid: 8000,
    status: 'pending'
  };
}

/**
 * 创建测试爬取结果
 */
function createTestCrawlResult(platformUserId) {
  return {
    platform: 'xiaohongshu',
    platformUserId: platformUserId,
    nickname: '测试达人（更新后）',
    avatarUrl: 'https://example.com/new_avatar.jpg',
    followersCount: 15000,
    city: '上海',
    uniqueId: 'test_unique_id_updated',
    contactInfo: '<EMAIL>',
    videoStats: {
      videoCount: 120,
      totalPlay: 1500000,
      averagePlay: 12500
    },
    videoDetails: [],
    rawData: {},
    authorExtInfo: {
      content_theme_labels_180d: ['美妆', '护肤'],
      tags_relation: ['美妆博主', '护肤达人']
    },
    playMid: 10000
  };
}

/**
 * 测试任务ID赋值功能
 */
async function testTaskIdAssignment() {
  console.log('🧪 测试更新现有达人时任务ID赋值功能\n');
  console.log('=' * 60 + '\n');

  try {
    // 创建CrawlerManager实例
    const crawlerManager = new CrawlerManager();
    
    // 测试数据
    const originalTaskId = 100;
    const newTaskId = 200;
    const platformUserId = 'test_user_' + Date.now();

    console.log('📋 测试参数:');
    console.log(`   原始任务ID: ${originalTaskId}`);
    console.log(`   新任务ID: ${newTaskId}`);
    console.log(`   平台用户ID: ${platformUserId}`);
    console.log('');

    // 步骤1: 创建初始达人记录
    console.log('🔄 步骤1: 创建初始达人记录...');
    const initialData = createTestInfluencerData(originalTaskId, platformUserId);
    const initialInfluencer = await PublicInfluencer.create(initialData);
    
    console.log(`✅ 初始达人记录创建成功:`);
    console.log(`   记录ID: ${initialInfluencer.id}`);
    console.log(`   任务ID: ${initialInfluencer.taskId}`);
    console.log(`   昵称: ${initialInfluencer.nickname}`);
    console.log(`   粉丝数: ${initialInfluencer.followersCount}`);
    console.log('');

    // 步骤2: 模拟爬取结果更新
    console.log('🔄 步骤2: 模拟爬取结果更新...');
    const crawlResult = createTestCrawlResult(platformUserId);
    
    console.log(`📊 爬取结果数据:`);
    console.log(`   昵称: ${crawlResult.nickname}`);
    console.log(`   粉丝数: ${crawlResult.followersCount}`);
    console.log(`   城市: ${crawlResult.city}`);
    console.log(`   播放量中位数: ${crawlResult.playMid}`);
    console.log('');

    // 步骤3: 调用handleResult方法更新达人
    console.log('🔄 步骤3: 调用handleResult方法更新达人...');
    await crawlerManager.handleResult(newTaskId, crawlResult);
    console.log('✅ handleResult方法调用完成');
    console.log('');

    // 步骤4: 验证更新结果
    console.log('🔄 步骤4: 验证更新结果...');
    const updatedInfluencer = await PublicInfluencer.findByPk(initialInfluencer.id);
    
    if (!updatedInfluencer) {
      console.error('❌ 找不到更新后的达人记录');
      return;
    }

    console.log(`📊 更新后的达人记录:`);
    console.log(`   记录ID: ${updatedInfluencer.id}`);
    console.log(`   任务ID: ${updatedInfluencer.taskId}`);
    console.log(`   昵称: ${updatedInfluencer.nickname}`);
    console.log(`   粉丝数: ${updatedInfluencer.followersCount}`);
    console.log(`   城市: ${updatedInfluencer.city}`);
    console.log(`   播放量中位数: ${updatedInfluencer.playMid}`);
    console.log('');

    // 步骤5: 验证任务ID是否正确更新
    console.log('🔄 步骤5: 验证任务ID更新...');
    
    const taskIdUpdated = updatedInfluencer.taskId === newTaskId;
    const nicknameUpdated = updatedInfluencer.nickname === crawlResult.nickname;
    const followersUpdated = updatedInfluencer.followersCount === crawlResult.followersCount;
    const cityUpdated = updatedInfluencer.city === crawlResult.city;
    const playMidUpdated = updatedInfluencer.playMid === crawlResult.playMid;

    console.log('✅ 验证结果:');
    console.log(`   任务ID更新: ${taskIdUpdated ? '✅ 正确' : '❌ 错误'} (${originalTaskId} → ${updatedInfluencer.taskId})`);
    console.log(`   昵称更新: ${nicknameUpdated ? '✅ 正确' : '❌ 错误'}`);
    console.log(`   粉丝数更新: ${followersUpdated ? '✅ 正确' : '❌ 错误'}`);
    console.log(`   城市更新: ${cityUpdated ? '✅ 正确' : '❌ 错误'}`);
    console.log(`   播放量中位数更新: ${playMidUpdated ? '✅ 正确' : '❌ 错误'}`);
    console.log('');

    // 总体验证
    const allCorrect = taskIdUpdated && nicknameUpdated && followersUpdated && cityUpdated && playMidUpdated;
    console.log(`🎯 总体结果: ${allCorrect ? '✅ 所有更新都正确' : '❌ 存在更新错误'}`);
    console.log('');

    // 步骤6: 清理测试数据
    console.log('🔄 步骤6: 清理测试数据...');
    await updatedInfluencer.destroy();
    console.log('✅ 测试数据清理完成');
    console.log('');

    return {
      success: allCorrect,
      results: {
        taskIdUpdated,
        nicknameUpdated,
        followersUpdated,
        cityUpdated,
        playMidUpdated
      },
      originalTaskId,
      newTaskId,
      finalTaskId: updatedInfluencer.taskId
    };

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    console.error('错误详情:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试多个任务ID更新场景
 */
async function testMultipleTaskIdUpdates() {
  console.log('🧪 测试多个任务ID更新场景\n');
  console.log('-' * 40 + '\n');

  try {
    const crawlerManager = new CrawlerManager();
    const platformUserId = 'multi_test_user_' + Date.now();
    
    // 创建初始记录
    const initialTaskId = 300;
    const initialData = createTestInfluencerData(initialTaskId, platformUserId);
    const influencer = await PublicInfluencer.create(initialData);
    
    console.log(`📋 初始记录: 任务ID ${initialTaskId}`);

    // 模拟多次更新
    const updateTasks = [400, 500, 600];
    
    for (const taskId of updateTasks) {
      console.log(`🔄 更新为任务ID ${taskId}...`);
      
      const crawlResult = createTestCrawlResult(platformUserId);
      crawlResult.nickname = `测试达人_任务${taskId}`;
      crawlResult.followersCount = 10000 + taskId;
      
      await crawlerManager.handleResult(taskId, crawlResult);
      
      // 验证更新
      const updated = await PublicInfluencer.findByPk(influencer.id);
      const correct = updated.taskId === taskId;
      
      console.log(`   结果: ${correct ? '✅ 正确' : '❌ 错误'} (任务ID: ${updated.taskId})`);
    }

    // 清理
    await influencer.destroy();
    console.log('✅ 多任务更新测试完成\n');

  } catch (error) {
    console.error('❌ 多任务更新测试失败:', error.message);
  }
}

/**
 * 主测试函数
 */
async function runTaskIdAssignmentTests() {
  console.log('🚀 任务ID赋值功能测试\n');
  
  try {
    // 测试1: 基本任务ID赋值功能
    const basicTestResult = await testTaskIdAssignment();
    
    console.log('=' * 60 + '\n');
    
    // 测试2: 多个任务ID更新场景
    await testMultipleTaskIdUpdates();
    
    console.log('🏁 所有测试完成!\n');
    
    // 输出测试总结
    console.log('📋 测试总结:');
    if (basicTestResult.success) {
      console.log('   ✅ 基本任务ID赋值功能正常');
      console.log(`   ✅ 任务ID正确从 ${basicTestResult.originalTaskId} 更新为 ${basicTestResult.newTaskId}`);
    } else {
      console.log('   ❌ 基本任务ID赋值功能异常');
      if (basicTestResult.error) {
        console.log(`   错误: ${basicTestResult.error}`);
      }
    }
    
    console.log('   ✅ 多任务更新场景测试完成');
    console.log('');
    
    console.log('🎯 修复效果:');
    console.log('   - 更新现有达人时会正确设置当前任务ID');
    console.log('   - 可以追踪达人记录最后更新的任务来源');
    console.log('   - 保持数据的一致性和可追溯性');

  } catch (error) {
    console.error('❌ 测试套件执行失败:', error.message);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runTaskIdAssignmentTests().catch(console.error);
}

module.exports = {
  testTaskIdAssignment,
  testMultipleTaskIdUpdates,
  runTaskIdAssignmentTests
};
