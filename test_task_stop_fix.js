/**
 * 测试任务停止功能修复效果
 * 
 * 这个脚本用于验证任务停止失败问题的修复效果
 */

const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000/api';

/**
 * 获取测试用的JWT token
 */
async function getTestToken() {
  try {
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });

    if (loginResponse.data.success) {
      return loginResponse.data.data.token;
    } else {
      throw new Error('登录失败');
    }
  } catch (error) {
    console.error('❌ 获取测试token失败:', error.message);
    return null;
  }
}

/**
 * 诊断任务状态
 */
async function diagnoseTask(taskId, token) {
  console.log(`🔍 诊断任务 ${taskId} 状态...\n`);

  try {
    const response = await axios.get(`${API_BASE_URL}/crawler/tasks/${taskId}/diagnose`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ 诊断完成!');
    console.log('📊 诊断结果:');
    console.log(JSON.stringify(response.data.data, null, 2));
    console.log('');

    return response.data.data;

  } catch (error) {
    console.error('❌ 诊断失败:', error.message);
    if (error.response) {
      console.error('   响应状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
    return null;
  }
}

/**
 * 测试普通停止功能
 */
async function testNormalStop(taskId, token) {
  console.log(`⏹️ 测试普通停止任务 ${taskId}...\n`);

  try {
    const response = await axios.post(`${API_BASE_URL}/crawler/tasks/${taskId}/stop`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ 普通停止成功!');
    console.log('📊 响应数据:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('');

    return { success: true, data: response.data };

  } catch (error) {
    console.log('❌ 普通停止失败:', error.message);
    if (error.response) {
      console.log('   响应状态:', error.response.status);
      console.log('   响应数据:', JSON.stringify(error.response.data, null, 2));
    }
    return { success: false, error: error.message };
  }
}

/**
 * 测试强制停止功能
 */
async function testForceStop(taskId, token) {
  console.log(`🛑 测试强制停止任务 ${taskId}...\n`);

  try {
    const response = await axios.post(`${API_BASE_URL}/crawler/tasks/${taskId}/force-stop`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ 强制停止成功!');
    console.log('📊 响应数据:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('');

    return { success: true, data: response.data };

  } catch (error) {
    console.log('❌ 强制停止失败:', error.message);
    if (error.response) {
      console.log('   响应状态:', error.response.status);
      console.log('   响应数据:', JSON.stringify(error.response.data, null, 2));
    }
    return { success: false, error: error.message };
  }
}

/**
 * 获取任务详情
 */
async function getTaskDetail(taskId, token) {
  try {
    const response = await axios.get(`${API_BASE_URL}/crawler/tasks/${taskId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    return response.data.data;
  } catch (error) {
    console.error('❌ 获取任务详情失败:', error.message);
    return null;
  }
}

/**
 * 获取任务列表
 */
async function getTaskList(token) {
  try {
    const response = await axios.get(`${API_BASE_URL}/crawler/tasks`, {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params: {
        page: 1,
        limit: 10
      }
    });

    return response.data.data.tasks;
  } catch (error) {
    console.error('❌ 获取任务列表失败:', error.message);
    return [];
  }
}

/**
 * 主测试函数
 */
async function runStopTestSuite() {
  console.log('🚀 任务停止功能测试套件\n');
  console.log('=' * 60 + '\n');

  try {
    // 获取测试token
    console.log('🔑 获取测试token...');
    const token = await getTestToken();
    if (!token) {
      console.log('❌ 无法获取测试token，退出测试');
      return;
    }
    console.log('✅ 测试token获取成功\n');

    // 获取任务列表
    console.log('📋 获取任务列表...');
    const tasks = await getTaskList(token);
    console.log(`✅ 获取到 ${tasks.length} 个任务\n`);

    if (tasks.length === 0) {
      console.log('⚠️ 没有可测试的任务，请先创建一些任务');
      return;
    }

    // 显示任务列表
    console.log('📊 任务列表:');
    tasks.forEach((task, index) => {
      console.log(`   ${index + 1}. ID: ${task.id}, 名称: ${task.taskName}, 状态: ${task.status}`);
    });
    console.log('');

    // 选择一个任务进行测试（这里选择第一个）
    const testTask = tasks[0];
    const taskId = testTask.id;

    console.log(`🎯 选择任务 ${taskId} (${testTask.taskName}) 进行测试\n`);
    console.log('-' * 40 + '\n');

    // 步骤1: 诊断任务状态
    const diagnosis = await diagnoseTask(taskId, token);
    
    console.log('-' * 40 + '\n');

    // 步骤2: 尝试普通停止
    const normalStopResult = await testNormalStop(taskId, token);
    
    console.log('-' * 40 + '\n');

    // 如果普通停止失败，尝试强制停止
    if (!normalStopResult.success) {
      console.log('🔧 普通停止失败，尝试强制停止...\n');
      const forceStopResult = await testForceStop(taskId, token);
      
      console.log('-' * 40 + '\n');

      // 再次诊断状态
      console.log('🔍 强制停止后再次诊断状态...\n');
      await diagnoseTask(taskId, token);
    } else {
      console.log('✅ 普通停止成功，无需强制停止\n');
    }

    console.log('🏁 测试完成!\n');

    // 输出测试总结
    console.log('📋 测试总结:');
    console.log(`   测试任务: ${taskId} (${testTask.taskName})`);
    console.log(`   原始状态: ${testTask.status}`);
    console.log(`   普通停止: ${normalStopResult.success ? '✅ 成功' : '❌ 失败'}`);
    
    if (!normalStopResult.success) {
      console.log(`   失败原因: ${normalStopResult.error}`);
      console.log(`   强制停止: 已尝试`);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

/**
 * 测试特定任务ID
 */
async function testSpecificTask(taskId) {
  console.log(`🎯 测试特定任务 ${taskId}\n`);

  const token = await getTestToken();
  if (!token) {
    console.log('❌ 无法获取测试token');
    return;
  }

  // 获取任务详情
  console.log('📋 获取任务详情...');
  const task = await getTaskDetail(taskId, token);
  if (!task) {
    console.log('❌ 任务不存在');
    return;
  }

  console.log(`✅ 任务详情: ${task.taskName} (状态: ${task.status})\n`);

  // 诊断状态
  await diagnoseTask(taskId, token);
  
  console.log('-' * 40 + '\n');

  // 尝试停止
  const stopResult = await testNormalStop(taskId, token);
  
  if (!stopResult.success) {
    console.log('-' * 40 + '\n');
    await testForceStop(taskId, token);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  // 检查是否指定了特定任务ID
  const taskId = process.argv[2];
  
  if (taskId) {
    testSpecificTask(parseInt(taskId)).catch(console.error);
  } else {
    runStopTestSuite().catch(console.error);
  }
}

module.exports = {
  getTestToken,
  diagnoseTask,
  testNormalStop,
  testForceStop,
  getTaskDetail,
  getTaskList,
  runStopTestSuite,
  testSpecificTask
};
