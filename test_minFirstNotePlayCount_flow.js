/**
 * 测试minFirstNotePlayCount参数从前端到后端的完整数据流
 * 
 * 这个脚本用于验证首个帖子播放量过滤功能的数据传递是否正常
 */

const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000/api';
const TEST_TOKEN = 'your_test_token_here'; // 需要替换为实际的测试token

/**
 * 测试创建爬虫任务，验证minFirstNotePlayCount参数传递
 */
async function testCreateCrawlerTask() {
  console.log('🧪 开始测试minFirstNotePlayCount参数传递...\n');

  try {
    // 模拟前端提交的数据
    const testData = {
      taskName: '测试首个帖子播放量过滤',
      platform: 'xiaohongshu',
      keywords: '美妆测试',
      maxPages: 2,
      priority: 1,
      config: {
        minFirstNotePlayCount: 10000, // 设置1万播放量阈值
        pageSize: 10,
        saveVideos: false
      }
    };

    console.log('📋 测试数据:');
    console.log(JSON.stringify(testData, null, 2));
    console.log('');

    // 发送创建任务请求
    const response = await axios.post(`${API_BASE_URL}/crawler/tasks`, testData, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ 任务创建成功!');
    console.log('📊 响应数据:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('');

    // 验证返回的任务数据
    const task = response.data.data;
    if (task && task.config && task.config.minFirstNotePlayCount) {
      console.log(`🎯 minFirstNotePlayCount参数传递成功: ${task.config.minFirstNotePlayCount}`);
    } else {
      console.log('❌ minFirstNotePlayCount参数未正确传递');
      console.log('任务config:', task.config);
    }

    return task;

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    return null;
  }
}

/**
 * 测试获取任务详情，验证参数是否正确保存
 */
async function testGetTaskDetail(taskId) {
  console.log(`\n🔍 获取任务 ${taskId} 详情...\n`);

  try {
    const response = await axios.get(`${API_BASE_URL}/crawler/tasks/${taskId}`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });

    console.log('✅ 任务详情获取成功!');
    const task = response.data.data;
    
    console.log('📋 任务基本信息:');
    console.log(`   ID: ${task.id}`);
    console.log(`   名称: ${task.taskName}`);
    console.log(`   平台: ${task.platform}`);
    console.log(`   关键词: ${task.keywords}`);
    console.log(`   状态: ${task.status}`);
    console.log('');

    console.log('⚙️ 任务配置:');
    console.log(JSON.stringify(task.config, null, 2));
    console.log('');

    // 验证minFirstNotePlayCount参数
    if (task.config && task.config.minFirstNotePlayCount) {
      console.log(`🎯 minFirstNotePlayCount参数保存正确: ${task.config.minFirstNotePlayCount}`);
      return true;
    } else {
      console.log('❌ minFirstNotePlayCount参数未找到或为空');
      return false;
    }

  } catch (error) {
    console.error('❌ 获取任务详情失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 模拟前端表单提交数据
 */
function simulateFrontendFormData() {
  console.log('🎭 模拟前端表单数据构建过程...\n');

  // 模拟前端form对象
  const form = {
    id: '',
    taskName: '测试任务',
    platform: 'xiaohongshu',
    keywords: '美妆',
    maxPages: 3,
    priority: 1,
    minFirstNotePlayCount: 15000, // 用户设置的值
    config: {
      pageSize: 20,
      saveVideos: true
    }
  };

  console.log('📝 前端form对象:');
  console.log(JSON.stringify(form, null, 2));
  console.log('');

  // 模拟handleSubmit方法中的数据处理
  const submitData = {
    ...form,
    config: {
      ...form.config,
      minFirstNotePlayCount: form.minFirstNotePlayCount || 1
    }
  };

  console.log('📤 提交到后端的数据:');
  console.log(JSON.stringify(submitData, null, 2));
  console.log('');

  return submitData;
}

/**
 * 测试不同的minFirstNotePlayCount值
 */
async function testDifferentPlayCountValues() {
  console.log('🎯 测试不同的播放量阈值...\n');

  const testValues = [1, 1000, 5000, 10000, 50000];

  for (const value of testValues) {
    console.log(`📊 测试播放量阈值: ${value}`);

    const testData = {
      taskName: `测试播放量${value}`,
      platform: 'xiaohongshu',
      keywords: '测试',
      maxPages: 1,
      priority: 0,
      config: {
        minFirstNotePlayCount: value,
        pageSize: 5,
        saveVideos: false
      }
    };

    try {
      const response = await axios.post(`${API_BASE_URL}/crawler/tasks`, testData, {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });

      const task = response.data.data;
      const savedValue = task.config.minFirstNotePlayCount;
      
      if (savedValue === value) {
        console.log(`   ✅ 值 ${value} 传递正确`);
      } else {
        console.log(`   ❌ 值传递错误: 期望 ${value}, 实际 ${savedValue}`);
      }

    } catch (error) {
      console.log(`   ❌ 测试值 ${value} 失败: ${error.message}`);
    }

    console.log('');
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 minFirstNotePlayCount参数传递测试\n');
  console.log('=' * 60 + '\n');

  // 测试1: 模拟前端数据构建
  simulateFrontendFormData();
  
  console.log('-' * 40 + '\n');

  // 测试2: 创建任务并验证参数传递
  const task = await testCreateCrawlerTask();
  
  if (task) {
    console.log('-' * 40 + '\n');
    
    // 测试3: 获取任务详情验证参数保存
    await testGetTaskDetail(task.id);
  }

  console.log('-' * 40 + '\n');

  // 测试4: 测试不同的播放量值
  await testDifferentPlayCountValues();

  console.log('🏁 测试完成!\n');
  
  console.log('📋 检查清单:');
  console.log('   □ 前端form对象包含minFirstNotePlayCount字段');
  console.log('   □ handleSubmit方法正确将参数添加到config中');
  console.log('   □ 后端API正确接收config参数');
  console.log('   □ 数据库正确保存config.minFirstNotePlayCount');
  console.log('   □ CrawlerManager正确传递参数给爬虫');
  console.log('   □ 爬虫正确使用参数进行过滤');
}

/**
 * 调试信息输出
 */
function printDebugInfo() {
  console.log('🔧 调试信息:\n');
  
  console.log('数据流路径:');
  console.log('1. 前端 form.minFirstNotePlayCount');
  console.log('2. handleSubmit() → submitData.config.minFirstNotePlayCount');
  console.log('3. crawlerAPI.create(submitData)');
  console.log('4. 后端 CrawlerController.createTask()');
  console.log('5. crawlerService.createTask() → 保存到数据库');
  console.log('6. CrawlerManager.executeCrawlingTask() → crawlConfig.minFirstNotePlayCount');
  console.log('7. XiaohongshuCrawler.crawl() → config.minFirstNotePlayCount');
  console.log('8. processAuthorsInBatches() → options.minFirstNotePlayCount');
  console.log('');

  console.log('可能的问题点:');
  console.log('- 前端表单字段绑定错误');
  console.log('- handleSubmit方法数据构建错误');
  console.log('- 后端API参数接收错误');
  console.log('- 数据库保存时config字段处理错误');
  console.log('- CrawlerManager配置构建时遗漏参数');
  console.log('- 爬虫接收参数时路径错误');
  console.log('');
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  printDebugInfo();
  runTests().catch(console.error);
}

module.exports = {
  testCreateCrawlerTask,
  testGetTaskDetail,
  simulateFrontendFormData,
  testDifferentPlayCountValues,
  runTests,
  printDebugInfo
};
