/**
 * 测试状态不一致任务修复功能
 * 
 * 这个脚本用于验证状态不一致任务的自动修复功能
 */

const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000/api';

/**
 * 获取测试用的JWT token
 */
async function getTestToken() {
  try {
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });

    if (loginResponse.data.success) {
      return loginResponse.data.data.token;
    } else {
      throw new Error('登录失败');
    }
  } catch (error) {
    console.error('❌ 获取测试token失败:', error.message);
    return null;
  }
}

/**
 * 获取任务列表
 */
async function getTaskList(token) {
  try {
    const response = await axios.get(`${API_BASE_URL}/crawler/tasks`, {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params: {
        page: 1,
        limit: 20
      }
    });

    return response.data.data.tasks;
  } catch (error) {
    console.error('❌ 获取任务列表失败:', error.message);
    return [];
  }
}

/**
 * 检查状态不一致的任务
 */
async function checkInconsistentTasks(token) {
  console.log('🔍 检查状态不一致的任务...\n');

  try {
    const response = await axios.post(`${API_BASE_URL}/crawler/tasks/check-inconsistent`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ 检查完成!');
    console.log('📊 检查结果:');
    console.log(JSON.stringify(response.data.data, null, 2));
    console.log('');

    return response.data.data;

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    if (error.response) {
      console.error('   响应状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
    return null;
  }
}

/**
 * 诊断特定任务
 */
async function diagnoseTask(taskId, token) {
  console.log(`🔍 诊断任务 ${taskId}...\n`);

  try {
    const response = await axios.get(`${API_BASE_URL}/crawler/tasks/${taskId}/diagnose`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ 诊断完成!');
    console.log('📊 诊断结果:');
    console.log(JSON.stringify(response.data.data, null, 2));
    console.log('');

    return response.data.data;

  } catch (error) {
    console.error('❌ 诊断失败:', error.message);
    return null;
  }
}

/**
 * 尝试停止任务
 */
async function tryStopTask(taskId, token) {
  console.log(`⏹️ 尝试停止任务 ${taskId}...\n`);

  try {
    const response = await axios.post(`${API_BASE_URL}/crawler/tasks/${taskId}/stop`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ 停止成功!');
    console.log('📊 响应数据:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('');

    return { success: true, data: response.data };

  } catch (error) {
    console.log('❌ 停止失败:', error.message);
    if (error.response) {
      console.log('   响应状态:', error.response.status);
      console.log('   响应数据:', JSON.stringify(error.response.data, null, 2));
    }
    return { success: false, error: error.message };
  }
}

/**
 * 显示任务状态统计
 */
function showTaskStatistics(tasks) {
  console.log('📊 任务状态统计:');
  
  const statusCount = {};
  tasks.forEach(task => {
    statusCount[task.status] = (statusCount[task.status] || 0) + 1;
  });

  Object.entries(statusCount).forEach(([status, count]) => {
    console.log(`   ${status}: ${count} 个`);
  });
  console.log(`   总计: ${tasks.length} 个任务`);
  console.log('');

  // 显示running状态的任务详情
  const runningTasks = tasks.filter(task => task.status === 'running');
  if (runningTasks.length > 0) {
    console.log('🏃 Running状态的任务:');
    runningTasks.forEach(task => {
      console.log(`   ID: ${task.id}, 名称: ${task.taskName}, 开始时间: ${task.startedAt}`);
    });
    console.log('');
  }

  return { statusCount, runningTasks };
}

/**
 * 主测试函数
 */
async function runInconsistentTaskTest() {
  console.log('🚀 状态不一致任务修复测试\n');
  console.log('=' * 60 + '\n');

  try {
    // 获取测试token
    console.log('🔑 获取测试token...');
    const token = await getTestToken();
    if (!token) {
      console.log('❌ 无法获取测试token，退出测试');
      return;
    }
    console.log('✅ 测试token获取成功\n');

    // 步骤1: 获取任务列表
    console.log('📋 获取任务列表...');
    const tasks = await getTaskList(token);
    console.log(`✅ 获取到 ${tasks.length} 个任务\n`);

    if (tasks.length === 0) {
      console.log('⚠️ 没有任务，无法进行测试');
      return;
    }

    // 步骤2: 显示任务状态统计
    const { statusCount, runningTasks } = showTaskStatistics(tasks);

    console.log('-' * 40 + '\n');

    // 步骤3: 检查状态不一致的任务
    const checkResult = await checkInconsistentTasks(token);

    console.log('-' * 40 + '\n');

    // 步骤4: 如果有running任务，尝试诊断和停止
    if (runningTasks.length > 0) {
      const testTask = runningTasks[0];
      console.log(`🎯 选择任务 ${testTask.id} (${testTask.taskName}) 进行测试\n`);

      // 诊断任务
      await diagnoseTask(testTask.id, token);

      console.log('-' * 40 + '\n');

      // 尝试停止任务
      const stopResult = await tryStopTask(testTask.id, token);

      console.log('-' * 40 + '\n');

      // 再次诊断
      if (!stopResult.success) {
        console.log('🔍 停止失败后再次诊断...\n');
        await diagnoseTask(testTask.id, token);
      }
    } else {
      console.log('ℹ️ 没有running状态的任务可供测试');
    }

    console.log('🏁 测试完成!\n');

    // 输出测试总结
    console.log('📋 测试总结:');
    console.log(`   总任务数: ${tasks.length}`);
    console.log(`   Running任务数: ${runningTasks.length}`);
    if (checkResult) {
      console.log(`   检查到的不一致任务: ${checkResult.inconsistentTasks}`);
      console.log(`   修复的任务数: ${checkResult.fixedTasks?.length || 0}`);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

/**
 * 测试特定任务
 */
async function testSpecificTask(taskId) {
  console.log(`🎯 测试特定任务 ${taskId}\n`);

  const token = await getTestToken();
  if (!token) {
    console.log('❌ 无法获取测试token');
    return;
  }

  // 诊断任务
  await diagnoseTask(taskId, token);
  
  console.log('-' * 40 + '\n');

  // 尝试停止
  const stopResult = await tryStopTask(taskId, token);
  
  if (!stopResult.success) {
    console.log('-' * 40 + '\n');
    console.log('🔧 停止失败，检查所有不一致任务...\n');
    await checkInconsistentTasks(token);
    
    console.log('-' * 40 + '\n');
    console.log('🔍 再次尝试停止...\n');
    await tryStopTask(taskId, token);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  // 检查是否指定了特定任务ID
  const taskId = process.argv[2];
  
  if (taskId) {
    testSpecificTask(parseInt(taskId)).catch(console.error);
  } else {
    runInconsistentTaskTest().catch(console.error);
  }
}

module.exports = {
  getTestToken,
  getTaskList,
  checkInconsistentTasks,
  diagnoseTask,
  tryStopTask,
  showTaskStatistics,
  runInconsistentTaskTest,
  testSpecificTask
};
