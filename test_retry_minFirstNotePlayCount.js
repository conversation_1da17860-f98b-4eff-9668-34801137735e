/**
 * 测试任务重试时minFirstNotePlayCount参数传递
 * 
 * 验证任务重试时是否正确保持minFirstNotePlayCount参数
 */

const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:3000/api';

/**
 * 获取测试用的JWT token
 */
async function getTestToken() {
  try {
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });

    if (loginResponse.data.success) {
      return loginResponse.data.data.token;
    } else {
      throw new Error('登录失败');
    }
  } catch (error) {
    console.error('❌ 获取测试token失败:', error.message);
    return null;
  }
}

/**
 * 创建测试任务
 */
async function createTestTask(token, minFirstNotePlayCount) {
  try {
    const testData = {
      taskName: `测试重试参数传递_${minFirstNotePlayCount}`,
      platform: 'xiaohongshu',
      keywords: '测试重试',
      maxPages: 1,
      priority: 1,
      config: {
        minFirstNotePlayCount: minFirstNotePlayCount,
        pageSize: 3,
        saveVideos: false
      }
    };

    console.log('📋 创建测试任务:');
    console.log(`   minFirstNotePlayCount: ${minFirstNotePlayCount}`);
    console.log(`   config: ${JSON.stringify(testData.config, null, 2)}`);

    const response = await axios.post(`${API_BASE_URL}/crawler/tasks`, testData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      const task = response.data.data;
      console.log(`✅ 任务创建成功: ID ${task.id}`);
      console.log(`   保存的config: ${JSON.stringify(task.config, null, 2)}`);
      return task;
    } else {
      throw new Error('任务创建失败');
    }
  } catch (error) {
    console.error('❌ 创建测试任务失败:', error.message);
    return null;
  }
}

/**
 * 获取任务详情
 */
async function getTaskDetail(taskId, token) {
  try {
    const response = await axios.get(`${API_BASE_URL}/crawler/tasks/${taskId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error('获取任务详情失败');
    }
  } catch (error) {
    console.error('❌ 获取任务详情失败:', error.message);
    return null;
  }
}

/**
 * 重试任务
 */
async function retryTask(taskId, token) {
  try {
    console.log(`🔄 重试任务 ${taskId}...`);

    const response = await axios.post(`${API_BASE_URL}/crawler/tasks/${taskId}/retry`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.data.success) {
      const task = response.data.data;
      console.log(`✅ 任务重试成功: ID ${task.id}`);
      console.log(`   重试后的config: ${JSON.stringify(task.config, null, 2)}`);
      return task;
    } else {
      throw new Error('任务重试失败');
    }
  } catch (error) {
    console.error('❌ 任务重试失败:', error.message);
    if (error.response) {
      console.error('   响应数据:', error.response.data);
    }
    return null;
  }
}

/**
 * 停止任务（模拟失败状态）
 */
async function stopTask(taskId, token) {
  try {
    console.log(`⏹️ 停止任务 ${taskId}...`);

    const response = await axios.post(`${API_BASE_URL}/crawler/tasks/${taskId}/stop`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.data.success) {
      console.log(`✅ 任务停止成功`);
      return true;
    } else {
      throw new Error('任务停止失败');
    }
  } catch (error) {
    console.log(`⚠️ 任务停止失败: ${error.message}`);
    // 停止失败不影响测试继续
    return false;
  }
}

/**
 * 等待任务状态变化
 */
async function waitForTaskStatus(taskId, token, targetStatus, maxWaitTime = 30000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    const task = await getTaskDetail(taskId, token);
    if (task && task.status === targetStatus) {
      return task;
    }
    
    console.log(`   等待任务状态变为 ${targetStatus}，当前状态: ${task?.status || '未知'}`);
    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
  }
  
  throw new Error(`等待任务状态变为 ${targetStatus} 超时`);
}

/**
 * 测试任务重试参数传递
 */
async function testRetryParameterPersistence() {
  console.log('🧪 测试任务重试时minFirstNotePlayCount参数传递\n');
  console.log('=' * 60 + '\n');

  try {
    // 获取测试token
    console.log('🔑 获取测试token...');
    const token = await getTestToken();
    if (!token) {
      console.log('❌ 无法获取测试token，退出测试');
      return;
    }
    console.log('✅ 测试token获取成功\n');

    // 测试不同的minFirstNotePlayCount值
    const testValues = [5000, 10000, 50000];
    const testResults = [];

    for (const testValue of testValues) {
      console.log(`🎯 测试值: ${testValue}\n`);

      // 步骤1: 创建任务
      const task = await createTestTask(token, testValue);
      if (!task) {
        console.log(`❌ 任务创建失败，跳过测试值 ${testValue}\n`);
        continue;
      }

      // 验证创建时的参数
      const originalMinPlayCount = task.config?.minFirstNotePlayCount;
      console.log(`📊 原始任务参数: ${originalMinPlayCount}`);

      // 步骤2: 等待任务开始运行（或直接停止）
      console.log('⏳ 等待任务状态变化...');
      await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒

      // 步骤3: 停止任务（模拟失败）
      await stopTask(task.id, token);

      // 步骤4: 获取任务详情，确认状态
      let taskDetail = await getTaskDetail(task.id, token);
      console.log(`📋 停止后任务状态: ${taskDetail?.status}`);
      console.log(`📋 停止后config: ${JSON.stringify(taskDetail?.config, null, 2)}`);

      // 步骤5: 重试任务
      const retriedTask = await retryTask(task.id, token);
      if (!retriedTask) {
        console.log(`❌ 任务重试失败，跳过验证\n`);
        continue;
      }

      // 步骤6: 验证重试后的参数
      const retriedMinPlayCount = retriedTask.config?.minFirstNotePlayCount;
      console.log(`📊 重试后任务参数: ${retriedMinPlayCount}`);

      // 步骤7: 再次获取任务详情确认
      taskDetail = await getTaskDetail(task.id, token);
      const finalMinPlayCount = taskDetail?.config?.minFirstNotePlayCount;
      console.log(`📊 最终任务参数: ${finalMinPlayCount}`);

      // 验证结果
      const parameterPreserved = originalMinPlayCount === finalMinPlayCount;
      console.log(`🔍 参数保持检查: ${parameterPreserved ? '✅ 正确' : '❌ 错误'}`);
      console.log(`   原始值: ${originalMinPlayCount}`);
      console.log(`   最终值: ${finalMinPlayCount}`);

      testResults.push({
        testValue,
        originalValue: originalMinPlayCount,
        finalValue: finalMinPlayCount,
        preserved: parameterPreserved,
        taskId: task.id
      });

      // 步骤8: 清理 - 再次停止任务
      await stopTask(task.id, token);

      console.log(`\n${'─'.repeat(40)}\n`);
    }

    // 输出测试总结
    console.log('📋 测试总结:');
    console.log(`   测试用例数: ${testResults.length}`);
    
    const successCount = testResults.filter(r => r.preserved).length;
    const failCount = testResults.length - successCount;
    
    console.log(`   成功保持参数: ${successCount}`);
    console.log(`   参数丢失: ${failCount}`);
    console.log('');

    testResults.forEach((result, index) => {
      const status = result.preserved ? '✅' : '❌';
      console.log(`   ${index + 1}. ${status} 测试值 ${result.testValue}: ${result.originalValue} → ${result.finalValue}`);
    });

    console.log('');
    
    if (failCount === 0) {
      console.log('🎉 所有测试用例都成功保持了minFirstNotePlayCount参数！');
    } else {
      console.log(`⚠️ 有 ${failCount} 个测试用例的参数丢失，需要检查修复效果`);
    }

    return {
      totalTests: testResults.length,
      successCount,
      failCount,
      results: testResults
    };

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    return null;
  }
}

/**
 * 测试特定任务的重试
 */
async function testSpecificTaskRetry(taskId) {
  console.log(`🎯 测试特定任务 ${taskId} 的重试参数传递\n`);

  const token = await getTestToken();
  if (!token) {
    console.log('❌ 无法获取测试token');
    return;
  }

  // 获取任务详情
  console.log('📋 获取任务详情...');
  const task = await getTaskDetail(taskId, token);
  if (!task) {
    console.log('❌ 任务不存在');
    return;
  }

  console.log(`✅ 任务详情:`);
  console.log(`   ID: ${task.id}`);
  console.log(`   名称: ${task.taskName}`);
  console.log(`   状态: ${task.status}`);
  console.log(`   config: ${JSON.stringify(task.config, null, 2)}`);
  console.log(`   minFirstNotePlayCount: ${task.config?.minFirstNotePlayCount}`);
  console.log('');

  // 重试任务
  const retriedTask = await retryTask(taskId, token);
  if (retriedTask) {
    console.log('📊 重试后参数对比:');
    console.log(`   重试前: ${task.config?.minFirstNotePlayCount}`);
    console.log(`   重试后: ${retriedTask.config?.minFirstNotePlayCount}`);
    console.log(`   参数保持: ${task.config?.minFirstNotePlayCount === retriedTask.config?.minFirstNotePlayCount ? '✅ 是' : '❌ 否'}`);
  }
}

/**
 * 主测试函数
 */
async function runRetryParameterTests() {
  console.log('🚀 任务重试参数传递测试\n');
  
  try {
    // 检查是否指定了特定任务ID
    const taskId = process.argv[2];
    
    if (taskId) {
      await testSpecificTaskRetry(parseInt(taskId));
    } else {
      const results = await testRetryParameterPersistence();
      
      console.log('🏁 测试完成!\n');
      
      if (results) {
        console.log('🎯 修复效果:');
        console.log('   - TaskQueue.js 已添加 minFirstNotePlayCount 参数');
        console.log('   - 任务重试时正确保持配置参数');
        console.log('   - 参数传递链路完整：数据库 → TaskQueue → 爬虫');
      }
    }

  } catch (error) {
    console.error('❌ 测试套件执行失败:', error.message);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runRetryParameterTests().catch(console.error);
}

module.exports = {
  testRetryParameterPersistence,
  testSpecificTaskRetry,
  runRetryParameterTests
};
