/**
 * 检查达人提报表结构和数据
 */

const { sequelize, InfluencerReport, User } = require('./src/models');

async function checkInfluencerReportTable() {
  try {
    console.log('🔍 检查达人提报表结构和数据...\n');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 检查表是否存在
    const tableExists = await sequelize.getQueryInterface().showAllTables();
    console.log('📋 数据库中的表:', tableExists);

    if (tableExists.includes('influencer_reports')) {
      console.log('✅ influencer_reports 表存在');
      
      // 检查表结构
      const tableDescription = await sequelize.getQueryInterface().describeTable('influencer_reports');
      console.log('\n📊 influencer_reports 表结构:');
      Object.entries(tableDescription).forEach(([column, info]) => {
        console.log(`  ${column}: ${info.type} ${info.allowNull ? '' : 'NOT NULL'} ${info.defaultValue ? `DEFAULT ${info.defaultValue}` : ''}`);
      });

      // 检查现有记录数量
      const count = await InfluencerReport.count();
      console.log(`\n📈 现有记录数量: ${count}`);

      // 如果有记录，显示最近的几条
      if (count > 0) {
        const recentReports = await InfluencerReport.findAll({
          limit: 3,
          order: [['createdAt', 'DESC']],
          include: [
            {
              model: User,
              as: 'submitter',
              attributes: ['id', 'username', 'email']
            }
          ]
        });

        console.log('\n📋 最近的提报记录:');
        recentReports.forEach((report, index) => {
          console.log(`  ${index + 1}. ${report.influencerName} (${report.platform}) - ${report.status}`);
          console.log(`     提报人: ${report.submitter?.username || '未知'}`);
          console.log(`     创建时间: ${report.createdAt}`);
        });
      }

    } else {
      console.log('❌ influencer_reports 表不存在');
      
      // 尝试创建表
      console.log('🔧 尝试同步数据库模型...');
      await sequelize.sync({ alter: true });
      console.log('✅ 数据库模型同步完成');
    }

    // 检查用户表
    if (tableExists.includes('users')) {
      console.log('\n✅ users 表存在');
      const userCount = await User.count();
      console.log(`📈 用户数量: ${userCount}`);
      
      if (userCount > 0) {
        const users = await User.findAll({
          attributes: ['id', 'username', 'role'],
          limit: 5
        });
        console.log('👥 用户列表:');
        users.forEach(user => {
          console.log(`  ${user.id}: ${user.username} (${user.role})`);
        });
      }
    } else {
      console.log('❌ users 表不存在');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await sequelize.close();
    console.log('\n🔌 数据库连接已关闭');
  }
}

// 测试创建提报记录
async function testCreateReport() {
  try {
    console.log('\n🧪 测试创建提报记录...');

    await sequelize.authenticate();

    // 获取第一个用户
    const user = await User.findOne();
    if (!user) {
      console.log('❌ 没有找到用户，无法测试');
      return;
    }

    console.log(`✅ 找到测试用户: ${user.username} (ID: ${user.id})`);

    // 测试数据
    const testData = {
      platform: 'xiaohongshu',
      influencerName: '测试达人_' + Date.now(),
      operationManager: 'admin',
      influencerUrl: 'https://www.xiaohongshu.com/user/profile/test',
      followersCount: 10000,
      playMid: '5000',
      selectionReason: '这是一个测试选择理由，用于验证数据库创建功能。',
      platformPrice: '1000元/条',
      cooperationPrice: '800元/条',
      notes: '测试备注',
      status: 'submitting',
      submittedBy: user.id,
      influencerId: null,
      publicInfluencerId: null,
      lastSubmittedAt: new Date()
    };

    console.log('📤 创建测试提报记录...');
    const report = await InfluencerReport.create(testData);
    console.log('✅ 创建成功，记录ID:', report.id);

    // 查询刚创建的记录
    const createdReport = await InfluencerReport.findByPk(report.id, {
      include: [
        {
          model: User,
          as: 'submitter',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    console.log('📋 创建的记录详情:');
    console.log(`  达人名称: ${createdReport.influencerName}`);
    console.log(`  平台: ${createdReport.platform}`);
    console.log(`  提报人: ${createdReport.submitter.username}`);
    console.log(`  状态: ${createdReport.status}`);

    // 清理测试数据
    await report.destroy();
    console.log('🗑️ 测试数据已清理');

  } catch (error) {
    console.error('❌ 测试创建失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行检查
if (require.main === module) {
  (async () => {
    try {
      await checkInfluencerReportTable();
      await testCreateReport();
      console.log('🎉 检查完成');
      process.exit(0);
    } catch (error) {
      console.error('💥 检查失败:', error);
      process.exit(1);
    }
  })();
}

module.exports = { checkInfluencerReportTable, testCreateReport };
