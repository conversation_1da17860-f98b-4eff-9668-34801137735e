/**
 * 调试播放量中位数数据流程
 * 追踪 playMid 在整个爬虫流程中的传递
 */

const { sequelize, PublicInfluencer, CrawlTask } = require('./src/models');
const XingtuCrawler = require('./src/services/crawler/crawlers/XingtuCrawler');
const CrawlerManager = require('./src/services/crawler/CrawlerManager');

async function debugPlayMidFlow() {
  try {
    console.log('🔍 开始调试播放量中位数数据流程...\n');

    // 1. 测试 XingtuCrawler.getAuthorDetail 方法
    console.log('📋 步骤1: 测试 XingtuCrawler.getAuthorDetail');
    const crawler = new XingtuCrawler();
    await crawler.initialize();
    
    const testAuthorId = '6854377107594674189'; // 一号记录车
    const mockAuthor = { attribute_datas: {} };
    
    const authorDetail = await crawler.getAuthorDetail(testAuthorId, mockAuthor);
    
    console.log('✅ getAuthorDetail 返回结果:');
    console.log('   昵称:', authorDetail?.nickname);
    console.log('   播放量中位数:', authorDetail?.playMid);
    console.log('   包含playMid字段:', 'playMid' in (authorDetail || {}));
    console.log('   playMid类型:', typeof authorDetail?.playMid);
    console.log('');

    if (!authorDetail?.playMid) {
      console.log('❌ 问题发现: getAuthorDetail 没有返回 playMid');
      return;
    }

    // 2. 测试 CrawlerManager.handleResult 方法
    console.log('📋 步骤2: 测试 CrawlerManager.handleResult');
    
    // 创建测试任务
    const testTask = await CrawlTask.create({
      taskName: '播放量中位数调试任务',
      platform: 'juxingtu',
      keywords: 'debug',
      status: 'running',
      createdBy: 1
    });

    console.log('✅ 创建测试任务:', testTask.id);

    // 模拟传递给 handleResult 的数据
    const testResult = {
      platform: authorDetail.platform,
      platformUserId: authorDetail.platformUserId,
      nickname: authorDetail.nickname,
      avatarUrl: authorDetail.avatarUrl,
      followersCount: authorDetail.followersCount,
      city: authorDetail.city,
      uniqueId: authorDetail.uniqueId,
      contactInfo: authorDetail.contactInfo,
      videoStats: authorDetail.videoStats,
      videoDetails: authorDetail.videoDetails || {},
      rawData: authorDetail.rawData,
      authorExtInfo: authorDetail.authorExtInfo,
      playMid: authorDetail.playMid // 关键字段
    };

    console.log('📤 传递给 handleResult 的数据:');
    console.log('   昵称:', testResult.nickname);
    console.log('   播放量中位数:', testResult.playMid);
    console.log('   包含playMid字段:', 'playMid' in testResult);
    console.log('   playMid类型:', typeof testResult.playMid);
    console.log('');

    // 调用 CrawlerManager.handleResult
    const crawlerManager = new CrawlerManager();
    await crawlerManager.handleResult(testTask.id, testResult);

    console.log('✅ handleResult 调用完成');

    // 3. 验证数据库保存结果
    console.log('📋 步骤3: 验证数据库保存结果');
    
    const savedRecord = await PublicInfluencer.findOne({
      where: { 
        taskId: testTask.id,
        platformUserId: testAuthorId
      },
      order: [['createdAt', 'DESC']]
    });

    if (savedRecord) {
      console.log('✅ 数据库记录找到:');
      console.log('   记录ID:', savedRecord.id);
      console.log('   昵称:', savedRecord.nickname);
      console.log('   播放量中位数:', savedRecord.playMid);
      console.log('   playMid类型:', typeof savedRecord.playMid);
      console.log('   原始playMid值:', JSON.stringify(savedRecord.playMid));
      
      if (savedRecord.playMid === testResult.playMid) {
        console.log('✅ 播放量中位数保存正确！');
      } else {
        console.log('❌ 播放量中位数保存错误！');
        console.log('   期望值:', testResult.playMid);
        console.log('   实际值:', savedRecord.playMid);
      }

      // 清理测试数据
      await savedRecord.destroy();
    } else {
      console.log('❌ 数据库记录未找到');
    }

    // 清理测试任务
    await testTask.destroy();
    console.log('✅ 测试数据清理完成');

    // 4. 检查现有记录的问题
    console.log('\n📋 步骤4: 检查现有记录');
    
    const existingRecord = await PublicInfluencer.findOne({
      where: { platformUserId: testAuthorId },
      order: [['createdAt', 'DESC']]
    });

    if (existingRecord) {
      console.log('📊 现有记录信息:');
      console.log('   记录ID:', existingRecord.id);
      console.log('   昵称:', existingRecord.nickname);
      console.log('   播放量中位数:', existingRecord.playMid);
      console.log('   状态:', existingRecord.status);
      console.log('   创建时间:', existingRecord.createdAt.toLocaleString('zh-CN'));
      
      // 尝试手动更新这条记录
      console.log('\n🔧 尝试手动更新现有记录...');
      await PublicInfluencer.update(
        { playMid: authorDetail.playMid },
        { where: { id: existingRecord.id } }
      );
      
      // 验证更新结果
      const updatedRecord = await PublicInfluencer.findByPk(existingRecord.id);
      console.log('✅ 更新后的播放量中位数:', updatedRecord.playMid);
    }

    console.log('\n🎉 调试完成！');

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    console.error(error.stack);
  } finally {
    await sequelize.close();
    console.log('\n🔒 数据库连接已关闭');
  }
}

// 运行调试
if (require.main === module) {
  debugPlayMidFlow();
}

module.exports = debugPlayMidFlow;
