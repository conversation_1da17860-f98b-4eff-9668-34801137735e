import { Message } from '@arco-design/web-vue';

/**
 * 平台工具函数
 * 提供平台相关的通用功能
 */

/**
 * 获取平台显示名称
 * @param {string} platform - 平台标识
 * @returns {string} 平台显示名称
 */
export const getPlatformName = platform => {
  const platformMap = {
    xiaohongshu: '小红书',
    juxingtu: '巨量星图'
  };
  return platformMap[platform] || platform;
};

/**
 * 获取平台颜色
 * @param {string} platform - 平台标识
 * @returns {string} 平台对应的颜色
 */
export const getPlatformColor = platform => {
  const colorMap = {
    xiaohongshu: 'red',
    juxingtu: 'blue'
  };
  return colorMap[platform] || 'gray';
};

/**
 * 打开达人详情页面
 * 根据平台类型生成对应的跳转链接并在新标签页中打开
 * @param {Object} record - 达人记录对象
 * @param {string} record.platformUserId - 平台用户ID
 * @param {string} record.platform - 平台类型
 * @param {Object} [fallbackTask] - 备用任务对象，用于获取平台信息
 * @param {string} [fallbackTask.platform] - 备用平台类型
 */
export const openInfluencerDetailPage = (record, fallbackTask = null) => {
  // 检查是否有platformUserId
  if (!record.platformUserId && !record.platformId) {
    Message.warning('该达人缺少平台ID信息，无法跳转到详情页面');
    return;
  }

  // 获取平台信息，优先使用记录中的platform，如果没有则使用备用任务的platform
  const platform = record.platform || fallbackTask?.platform;

  if (!platform) {
    Message.warning('无法确定达人所属平台，无法跳转到详情页面');
    return;
  }

  // 根据平台类型生成对应的跳转链接
  let detailUrl = '';

  if (platform === 'xiaohongshu') {
    // 小红书平台跳转链接
    detailUrl = `https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/${
      record.platformUserId || record.platformId
    }?source=Advertiser_Kol`;
  } else if (platform === 'juxingtu') {
    // 抖音/巨量星图平台跳转链接
    detailUrl = `https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/${
      record.platformUserId || record.platformId
    }`;
  } else {
    Message.warning(`暂不支持 ${getPlatformName(platform)} 平台的详情页面跳转`);
    return;
  }

  // 在新标签页中打开链接
  window.open(detailUrl, '_blank');
};

/**
 * 格式化数字显示
 * @param {number} num - 数字
 * @returns {string} 格式化后的数字字符串
 */
export const formatNumber = num => {
  if (!num) return '0';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toLocaleString();
};

/**
 * 格式化价格显示
 * @param {number|string} price - 价格
 * @returns {string} 格式化后的价格字符串
 */
export const formatPrice = price => {
  if (!price) return '未知';
  if (typeof price === 'string') return price;
  if (typeof price === 'number') return `¥${price.toLocaleString()}`;
  return '未知';
};

/**
 * 格式化日期显示
 * @param {string} dateStr - 日期字符串
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = dateStr => {
  if (!dateStr) return '-';
  return new Date(dateStr).toLocaleString('zh-CN');
};

/**
 * 解析达人链接或ID，提取平台用户ID
 * @param {string} input - 用户输入的链接或ID
 * @returns {Object} 解析结果 { platform: string, platformUserId: string, error?: string }
 */
export const parseInfluencerInput = input => {
  if (!input || typeof input !== 'string') {
    return { error: '请输入有效的达人链接或ID' };
  }

  const trimmedInput = input.trim();

  // 小红书蒲公英平台链接解析
  // 格式1: https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/5e7e8e8e8e8e8e8e?source=Advertiser_Kol
  // 格式2: https://pgy.xiaohongshu.com/solar/cooperator/blogger/5e7e8e8e8e8e8e8e
  const xiaohongshuPatterns = [
    /https?:\/\/pgy\.xiaohongshu\.com\/solar\/pre-trade\/blogger-detail\/([a-zA-Z0-9]+)/,
    /https?:\/\/pgy\.xiaohongshu\.com\/solar\/cooperator\/blogger\/([a-zA-Z0-9]+)/,
    /https?:\/\/www\.xiaohongshu\.com\/user\/profile\/([a-zA-Z0-9]+)/
  ];

  for (const pattern of xiaohongshuPatterns) {
    const match = trimmedInput.match(pattern);
    if (match) {
      return {
        platform: 'xiaohongshu',
        platformUserId: match[1]
      };
    }
  }

  // 巨量星图平台链接解析
  // 格式1: https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/123456789
  // 格式2: https://www.xingtu.cn/ad/creator/author-homepage/123456789
  const juxingtuPatterns = [
    /https?:\/\/www\.xingtu\.cn\/ad\/creator\/author-homepage\/douyin-video\/([0-9]+)/,
    /https?:\/\/www\.xingtu\.cn\/ad\/creator\/author-homepage\/([0-9]+)/
  ];

  for (const pattern of juxingtuPatterns) {
    const match = trimmedInput.match(pattern);
    if (match) {
      return {
        platform: 'juxingtu',
        platformUserId: match[1]
      };
    }
  }

  // 尝试直接解析为ID
  // 小红书ID通常是24位字符串（字母数字混合）
  if (/^[a-zA-Z0-9]{20,30}$/.test(trimmedInput)) {
    return {
      platform: 'xiaohongshu',
      platformUserId: trimmedInput
    };
  }

  // 巨量星图ID通常是纯数字
  if (/^[0-9]{8,20}$/.test(trimmedInput)) {
    return {
      platform: 'juxingtu',
      platformUserId: trimmedInput
    };
  }

  return {
    error: '无法识别的链接格式或ID，请检查输入内容'
  };
};

/**
 * 验证平台用户ID格式
 * @param {string} platform - 平台类型
 * @param {string} platformUserId - 平台用户ID
 * @returns {boolean} 是否有效
 */
export const validatePlatformUserId = (platform, platformUserId) => {
  if (!platform || !platformUserId) return false;

  switch (platform) {
    case 'xiaohongshu':
      // 小红书ID通常是20-30位字母数字混合
      return /^[a-zA-Z0-9]{20,30}$/.test(platformUserId);
    case 'juxingtu':
      // 巨量星图ID通常是8-20位纯数字
      return /^[0-9]{8,20}$/.test(platformUserId);
    default:
      return false;
  }
};
