<template>
  <div class="crawler-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">爬虫管理</h2>
        <p class="page-description">管理爬虫任务，监控执行状态和结果</p>
      </div>
    </div>

    <!-- 任务统计卡片 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic title="总任务数" :value="stats.total" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic title="运行中" :value="stats.running" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic title="已完成" :value="stats.completed" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic title="失败" :value="stats.failed" />
        </a-card>
      </a-col>
    </a-row>

    <!-- 搜索和筛选 -->
    <a-card class="search-card">
      <a-form :model="searchForm" layout="inline" @submit="handleSearch">
        <a-form-item label="任务名称">
          <a-input v-model="searchForm.keyword" placeholder="请输入任务名称或关键词" style="width: 200px" allow-clear />
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model="searchForm.status" placeholder="请选择状态" style="width: 150px" allow-clear>
            <a-option value="pending">待执行</a-option>
            <a-option value="running">运行中</a-option>
            <a-option value="paused">已暂停</a-option>
            <a-option value="completed">已完成</a-option>
            <a-option value="failed">失败</a-option>
            <a-option value="cancelled">已取消</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="平台">
          <a-select v-model="searchForm.platform" placeholder="请选择平台" style="width: 180px" allow-clear>
            <a-option value="xiaohongshu">小红书</a-option>
            <a-option value="juxingtu">巨量星图</a-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">搜索</a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">重置</a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 任务列表 -->
    <a-card class="table-card">
      <div class="table-action">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <template #icon>
              <icon-plus />
            </template>
            创建任务
          </a-button>
        </a-space>
        <a-space> </a-space>
      </div>
      <a-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :border="false"
        class="layout-auto"
        :scroll="{ x: 'max-content' }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <!-- 平台列 -->
        <template #platform="{ record }">
          <a-tag :color="getPlatformColor(record.platform)">
            {{ getPlatformName(record.platform) }}
          </a-tag>
        </template>

        <!-- 进度列 -->
        <template #progress="{ record }">
          <a-progress :percent="record.progress / 100" :status="getProgressStatus(record.status)" size="small" />
        </template>

        <!-- 成功/总数列 -->
        <template #counts="{ record }">
          <span>{{ record.successCount || 0 }}</span>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <div class="table-action">
            <a-link
              v-if="record.status === 'pending' || record.status === 'paused' || record.status === 'cancelled'"
              type="text"
              size="small"
              @click="startTask(record.id)"
            >
              启动
            </a-link>
            <a-link v-if="record.status === 'running'" type="text" size="small" @click="pauseTask(record.id)">
              暂停
            </a-link>
            <a-link
              v-if="record.status === 'pending' || record.status === 'paused' || record.status === 'cancelled'"
              type="text"
              size="small"
              @click="editTask(record)"
            >
              编辑
            </a-link>
            <a-link type="text" size="small" @click="viewResults(record)"> 查看结果 </a-link>
            <a-link
              v-if="record.status === 'failed' || record.status === 'completed'"
              type="text"
              size="small"
              @click="retryTask(record.id)"
            >
              重试
            </a-link>
            <!-- <a-link type="text" size="small" @click="viewTaskDetail(record)"> 达人主页 </a-link> -->
            <!-- 添加库 -->
            <!-- <a-popconfirm
              v-if="record.status === 'completed'"
              :content="`确定要将该任务的所有结果（${record.successCount || 0}条）添加到达人库吗？`"
              position="left"
              @ok="importTaskAllResults(record)"
            >
              <a-link type="text" size="small"> 添加到达人库 </a-link>
            </a-popconfirm> -->
            <!-- <a-popconfirm content="确定要删除这个任务吗？" position="left" @ok="deleteTask(record.id)">
              <a-link type="text" size="small" status="danger"> 删除 </a-link>
            </a-popconfirm> -->
          </div>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑任务弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="isEditMode ? '编辑任务' : '创建任务'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="任务名称" field="taskName">
              <a-input v-model="form.taskName" placeholder="请输入任务名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="平台" field="platform">
              <a-select v-model="form.platform" placeholder="请选择平台">
                <a-option value="xiaohongshu">小红书</a-option>
                <a-option value="juxingtu">巨量星图</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="搜索关键词" field="keywords">
          <a-input v-model="form.keywords" placeholder="请输入搜索关键词，多个关键词用空格分隔" />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="最大页数" field="maxPages">
              <a-input-number
                v-model="form.maxPages"
                placeholder="请输入最大爬取页数"
                :min="1"
                :max="50"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="任务优先级" field="priority">
              <a-input-number
                v-model="form.priority"
                placeholder="数字越大优先级越高"
                :min="0"
                :max="10"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 首个帖子播放量过滤配置 -->
        <a-form-item label="首个帖子最低播放量" field="minFirstNotePlayCount">
          <div class="play-count-config">
            <a-input-number
              v-model="form.minFirstNotePlayCount"
              placeholder="请输入最低播放量要求"
              :min="1"
              :max="10000000"
              :precision="0"
              style="width: 200px"
            >
            </a-input-number>

            <!-- 帮助提示 -->
            <div class="help-text">
              <a-typography-text type="secondary" style="font-size: 12px">
                设置达人首个帖子的最低播放量要求，用于过滤低质量关联达人。
                数值越高过滤越严格，建议根据目标达人质量设置合适阈值。
              </a-typography-text>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="配置参数" field="configJson">
          <a-textarea
            v-model="configJson"
            placeholder='请输入JSON格式的配置参数，如：{"searchType": 1, "minFollowers": 1000}'
            :rows="4"
            :auto-size="{ minRows: 4, maxRows: 8 }"
            allow-clear
          />
          <template #extra>
            <div class="config-help">
              <a-typography-text type="secondary" :style="{ fontSize: '12px' }">
                支持的参数：searchType(搜索类型), pageSize(每页数量), retries(重试次数), delay(延迟设置)等
              </a-typography-text>
              <div class="config-actions" :style="{ marginTop: '8px' }">
                <a-button size="mini" type="text" @click="insertConfigTemplate"> 插入模板 </a-button>
                <a-button size="mini" type="text" @click="validateConfigJson"> 验证格式 </a-button>
                <a-button size="mini" type="text" @click="clearConfig"> 清空 </a-button>
              </div>
            </div>
          </template>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 查看结果弹窗 -->
    <a-modal v-model:visible="resultsModalVisible" title="任务结果" width="1000px" :footer="false">
      <div class="results-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="任务名称">{{ selectedTask?.taskName }}</a-descriptions-item>
          <a-descriptions-item label="执行状态">
            <a-tag :color="getStatusColor(selectedTask?.status)">
              {{ getStatusText(selectedTask?.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">{{ selectedTask?.startedAt }}</a-descriptions-item>
          <a-descriptions-item label="结束时间">{{ selectedTask?.completedAt }}</a-descriptions-item>
          <a-descriptions-item label="成功数量">{{ selectedTask?.successCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="总数量">{{ selectedTask?.totalCount || 0 }}</a-descriptions-item>
        </a-descriptions>

        <!-- 任务配置信息展示区域 -->
        <div class="task-config-section" v-if="selectedTask">
          <a-collapse :default-active-key="['config']" :bordered="false">
            <a-collapse-item key="config" header="任务配置信息">
              <div class="config-content">
                <a-descriptions :column="2" size="small" bordered>
                  <!-- 基础配置 -->
                  <a-descriptions-item label="搜索关键词">
                    <a-tag color="blue">{{ selectedTask.keywords || '-' }}</a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="最大页数">
                    {{ selectedTask.maxPages || '-' }}
                  </a-descriptions-item>
                  <a-descriptions-item label="任务优先级">
                    <a-tag :color="getPriorityColor(selectedTask.priority)">
                      {{ getPriorityText(selectedTask.priority) }}
                    </a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="平台类型">
                    <a-tag color="green">{{ getPlatformText(selectedTask.platform) }}</a-tag>
                  </a-descriptions-item>

                  <!-- 重点展示：首个帖子最低播放量 -->
                  <a-descriptions-item label="首个帖子最低播放量" class="highlight-config">
                    <div class="play-count-display">
                      <span class="formatted-value">{{
                        formatPlayCountDisplay(getMinFirstNotePlayCount(selectedTask))
                      }}</span>
                      <span v-if="getMinFirstNotePlayCount(selectedTask) > 1000" class="original-value">
                        ({{ getMinFirstNotePlayCount(selectedTask).toLocaleString() }})
                      </span>
                    </div>
                  </a-descriptions-item>

                  <!-- 其他配置参数 -->
                  <a-descriptions-item label="每页数量">
                    {{ selectedTask.config?.pageSize || 20 }}
                  </a-descriptions-item>
                  <a-descriptions-item label="保存视频">
                    <a-tag :color="selectedTask.config?.saveVideos !== false ? 'green' : 'red'">
                      {{ selectedTask.config?.saveVideos !== false ? '是' : '否' }}
                    </a-tag>
                  </a-descriptions-item>
                  <a-descriptions-item label="重试次数">
                    {{ selectedTask.config?.retries || 3 }}
                  </a-descriptions-item>

                  <!-- 动态展示其他config参数 -->
                  <template v-for="(value, key) in getOtherConfigParams(selectedTask)" :key="key">
                    <a-descriptions-item :label="getConfigParamLabel(key)">
                      {{ formatConfigValue(value) }}
                    </a-descriptions-item>
                  </template>
                </a-descriptions>
              </div>
            </a-collapse-item>
          </a-collapse>
        </div>

        <div class="results-data" v-if="selectedTask?.results">
          <h4>采集结果预览</h4>
          <a-table
            :columns="resultColumns"
            :data="selectedTask?.results || []"
            :pagination="resultsPagination"
            size="small"
            class="layout-auto"
            :scroll="{ x: 'max-content' }"
            @page-change="handleResultsPageChange"
            @page-size-change="handleResultsPageSizeChange"
          >
            <!-- 头像列 -->
            <template #avatar="{ record }">
              <a-avatar :size="36">
                <img v-if="record.avatarUrl" :src="record.avatarUrl" alt="avatar" @error="handleImageError" />
                <span v-else>{{ record.nickname?.charAt(0) }}</span>
              </a-avatar>
            </template>
            <!-- 是否导入到达人库 -->
            <template #status="{ record }">
              <a-tag :color="statusMap[record.status]?.color || 'gray'">
                {{ statusMap[record.status]?.text || record.status }}
              </a-tag>
            </template>
            <!-- 播放量中位数 -->
            <template #playMid="{ record }">
              <span v-if="record.playMid" class="play-mid-value">
                {{ formatNumber(record.playMid) }}
              </span>
              <span v-else class="no-data">-</span>
            </template>
            <!-- 操作列 -->
            <template #actions="{ record }">
              <a-link type="text" v-if="record.status !== 'imported'" size="small" @click="importResult(record)">
                收藏
              </a-link>
              <a-link type="text" size="small" @click="viewResultDetail(record)"> 达人主页 </a-link>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import { crawlerAPI } from '@/services/api';
import { IconPlus } from '@arco-design/web-vue/es/icon';
import { openInfluencerDetailPage, getPlatformName } from '@/utils/platformUtils';

// 表格列定义
const columns = [
  { title: '任务ID', dataIndex: 'id' },
  { title: '任务名称', dataIndex: 'taskName' },
  { title: '平台', dataIndex: 'platform', slotName: 'platform' },
  { title: '关键词', dataIndex: 'keywords' },
  { title: '状态', dataIndex: 'status', slotName: 'status' },
  { title: '进度', dataIndex: 'progress', width: 200, slotName: 'progress' },
  { title: '获取总数', dataIndex: 'counts', slotName: 'counts', align: 'right' },
  { title: '创建时间', dataIndex: 'createdAt' },
  { title: '操作', slotName: 'actions', align: 'center', fixed: 'right' }
];

// 结果表格列
const resultColumns = [
  { title: 'ID', dataIndex: 'id' },
  {
    title: '头像',
    dataIndex: 'avatarUrl',
    slotName: 'avatar',
    width: 80
  },
  { title: '状态', dataIndex: 'status', slotName: 'status' },
  { title: '达人昵称', dataIndex: 'nickname' },
  { title: '平台ID', dataIndex: 'platformUserId' },
  { title: '粉丝数', dataIndex: 'followersCount' },
  { title: '城市', dataIndex: 'city' },
  { title: '播放量/阅读量中位数', dataIndex: 'playMid', slotName: 'playMid', align: 'right' },
  { title: '操作', slotName: 'actions', align: 'center', fixed: 'right' }
];

const statusMap = {
  pending: {
    text: '待处理',
    color: 'gray'
  },
  processed: {
    text: '已获取',
    color: 'blue'
  },
  collected: {
    text: '已收藏',
    color: 'green'
  },
  failed: {
    text: '失败',
    color: 'red'
  },
  imported: {
    text: '已收藏',
    color: 'green'
  }
};

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const modalVisible = ref(false);
const resultsModalVisible = ref(false);
const formRef = ref();
const selectedTask = ref(null);

// 编辑模式状态
const isEditMode = ref(false);
const editingTaskId = ref(null);

// 统计数据
const stats = ref({
  total: 0,
  running: 0,
  completed: 0,
  failed: 0
});

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  platform: ''
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: true
});

const resultsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: true
});

// 表单数据
const form = reactive({
  id: '',
  taskName: '',
  platform: '',
  keywords: '',
  maxPages: 5,
  priority: 0,
  minFirstNotePlayCount: 1, // 首个帖子最低播放量，默认值为1
  config: {}
});

// 配置参数的JSON字符串（用于表单输入）
const configJson = computed({
  get: () => {
    try {
      return Object.keys(form.config).length > 0 ? JSON.stringify(form.config, null, 2) : '';
    } catch (error) {
      console.warn('Error stringifying config:', error);
      return '';
    }
  },
  set: value => {
    try {
      if (!value || value.trim() === '') {
        form.config = {};
        return;
      }

      const parsed = JSON.parse(value);
      if (typeof parsed === 'object' && parsed !== null) {
        form.config = parsed;
      } else {
        console.warn('Config must be an object');
      }
    } catch (error) {
      console.warn('Invalid JSON config:', error);
      // 不清空已有配置，让用户修正JSON格式
    }
  }
});

// 快捷选择播放量数值
const quickSelectValues = ref([
  { value: 1000, label: '1千' },
  { value: 5000, label: '5千' },
  { value: 10000, label: '1万' },
  { value: 50000, label: '5万' },
  { value: 100000, label: '10万' }
]);

// 表单验证规则
const rules = {
  taskName: [{ required: true, message: '请输入任务名称' }],
  platform: [{ required: true, message: '请选择平台' }],
  keywords: [{ required: true, message: '请输入搜索关键词' }],
  minFirstNotePlayCount: [
    {
      type: 'number',
      min: 1,
      max: 10000000,
      message: '播放量要求必须在1-1000万之间'
    }
  ]
};

// 获取状态颜色
const getStatusColor = status => {
  const colorMap = {
    pending: 'gray',
    running: 'blue',
    paused: 'orange',
    completed: 'green',
    failed: 'red',
    cancelled: 'purple'
  };
  return colorMap[status] || 'gray';
};

// 获取状态文本
const getStatusText = status => {
  const textMap = {
    pending: '待执行',
    running: '运行中',
    paused: '已暂停',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  };
  return textMap[status] || status;
};

// 获取进度状态
const getProgressStatus = status => {
  if (status === 'failed') return 'danger';
  if (status === 'completed') return 'success';
  if (status === 'running') return 'normal';
  return 'normal';
};

// 获取平台颜色和名称
const getPlatformColor = platform => {
  const colorMap = {
    xiaohongshu: 'pink',
    juxingtu: 'blue'
  };
  return colorMap[platform] || 'gray';
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...searchForm
    };

    const response = await crawlerAPI.getList(params);
    if (response.success) {
      tableData.value = response.data;
      if (response.pagination) {
        pagination.total = response.pagination.total;
      }
    }
  } catch (error) {
    console.error('Load data error:', error);
    Message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await crawlerAPI.getStats();
    if (response.success) {
      stats.value = response.data;
    }
  } catch (error) {
    console.error('Load stats error:', error);
    // 使用默认统计数据
    stats.value = {
      total: 0,
      running: 0,
      completed: 0,
      failed: 0
    };
  }
};

// 搜索和重置
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    platform: ''
  });
  handleSearch();
};

// 分页处理
const handlePageChange = page => {
  pagination.current = page;
  loadData();
};

const handlePageSizeChange = pageSize => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  loadData();
};

const handleResultsPageChange = page => {
  resultsPagination.current = page;
  viewResults(selectedTask.value);
};

const handleResultsPageSizeChange = pageSize => {
  resultsPagination.pageSize = pageSize;
  resultsPagination.current = 1;
  viewResults(selectedTask.value);
};

// 任务操作
const startTask = async id => {
  try {
    const response = await crawlerAPI.start(id);
    if (response.success) {
      Message.success('任务启动成功');
      loadData();
      loadStats();
    }
  } catch {
    Message.error('启动失败');
  }
};

const pauseTask = async id => {
  try {
    const response = await crawlerAPI.stop(id);
    if (response.success) {
      Message.success('任务暂停成功');
      loadData();
      loadStats();
    }
  } catch {
    Message.error('暂停失败');
  }
};

const retryTask = async id => {
  try {
    const response = await crawlerAPI.retry(id);
    if (response.success) {
      Message.success('任务重试成功');
      loadData();
      loadStats();
    }
  } catch (error) {
    console.error('Retry error:', error);
    Message.error(error.response.data.message || '重试失败');
  }
};

const deleteTask = async id => {
  try {
    const response = await crawlerAPI.delete(id);
    if (response.success) {
      Message.success('删除成功');
      loadData();
      loadStats();
    }
  } catch (error) {
    console.error('Delete error:', error);
    Message.error('删除失败');
  }
};

// 导入结果到达人库
const importResult = async record => {
  try {
    const response = await crawlerAPI.importResults(selectedTask.value.id, [record.id]);
    if (response.success) {
      Message.success('导入成功');
      viewResults(selectedTask.value);
    } else {
      Message.error(response.message || '导入失败');
    }
  } catch (error) {
    console.error('Import error:', error);
    Message.error('导入失败');
  }
};

// 编辑任务
const editTask = task => {
  isEditMode.value = true;
  editingTaskId.value = task.id;

  // 填充表单数据
  form.taskName = task.taskName;
  form.platform = task.platform;
  form.keywords = task.keywords;
  form.maxPages = task.maxPages;
  form.priority = task.priority;
  form.minFirstNotePlayCount = task.config?.minFirstNotePlayCount || 1;
  form.config = { ...task.config } || {};

  modalVisible.value = true;
};

// 弹窗操作
const showCreateModal = () => {
  isEditMode.value = false;
  editingTaskId.value = null;
  resetForm();
  modalVisible.value = true;
};

// const viewTaskDetail = async record => {
//   try {
//     const response = await crawlerAPI.getById(record.id);
//     if (response.success) {
//       // 显示任务详情，可以使用Message或者新的弹窗
//       Message.info(`任务详情：${JSON.stringify(response.data, null, 2)}`);
//     }
//   } catch (error) {
//     console.error('Get task detail error:', error);
//     Message.error('获取任务详情失败');
//   }
// };

const viewResults = async record => {
  try {
    selectedTask.value = record;

    // 获取任务结果数据
    const response = await crawlerAPI.getResults(record.id, {
      page: resultsPagination.current,
      limit: resultsPagination.pageSize
    });
    if (response.success) {
      selectedTask.value.results = response.data.list;
      resultsPagination.total = response.data.pagination.total;
    }

    resultsModalVisible.value = true;
  } catch (error) {
    console.error('Load results error:', error);
    selectedTask.value = record;
    resultsModalVisible.value = true;
  }
};

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate();
    if (valid) return;

    // 将minFirstNotePlayCount添加到config中
    const submitData = {
      ...form,
      config: {
        ...form.config,
        minFirstNotePlayCount: form.minFirstNotePlayCount || 1
      }
    };

    let response;
    if (isEditMode.value) {
      // 编辑模式：调用更新API
      response = await crawlerAPI.update(editingTaskId.value, submitData);
      if (response.success) {
        Message.success('更新成功');
        modalVisible.value = false;
        isEditMode.value = false;
        editingTaskId.value = null;
        loadData();
        loadStats();
      }
    } else {
      // 创建模式：调用创建API
      response = await crawlerAPI.create(submitData);
      if (response.success) {
        Message.success('创建成功');
        modalVisible.value = false;
        loadData();
        loadStats();
      }
    }
  } catch (error) {
    console.error('Submit error:', error);
    Message.error(isEditMode.value ? '更新失败' : '创建失败');
  }
};

const handleCancel = () => {
  modalVisible.value = false;
  isEditMode.value = false;
  editingTaskId.value = null;
  resetForm();
};

const resetForm = () => {
  Object.assign(form, {
    id: '',
    taskName: '',
    platform: '',
    keywords: '',
    maxPages: 5,
    priority: 0,
    minFirstNotePlayCount: 1, // 重置为默认值
    config: {}
  });
};

const importTaskAllResults = async record => {
  try {
    const response = await crawlerAPI.importTaskAllResults(record.id, []);
    if (response.success) {
      // 显示详细的成功信息
      const data = response.data;
      let successMessage = response.message || '导入成功';

      if (data && (data.successCount || data.skipCount || data.failedCount)) {
        const details = [];
        if (data.successCount > 0) details.push(`成功导入${data.successCount}个`);
        if (data.skipCount > 0) details.push(`跳过${data.skipCount}个已存在`);
        if (data.failedCount > 0) details.push(`${data.failedCount}个失败`);

        if (details.length > 0) {
          successMessage = `导入完成：${details.join('，')}`;
        }
      }

      Message.success(successMessage);
      loadData(); // 刷新任务列表
      viewResults(record); // 刷新结果列表
    } else {
      // 显示详细的错误信息
      let errorMessage = response.message || '导入失败';

      // 如果有详细的状态信息，显示更友好的错误
      if (response.data) {
        const data = response.data;
        if (data.totalResults !== undefined) {
          if (data.totalResults === 0) {
            errorMessage = '该任务没有任何爬虫结果数据，请先执行爬虫任务';
          } else if (data.availableForImport === 0) {
            const statusInfo = [];
            if (data.statusBreakdown) {
              const breakdown = data.statusBreakdown;
              if (breakdown.pending) statusInfo.push(`${breakdown.pending}个待处理`);
              if (breakdown.failed) statusInfo.push(`${breakdown.failed}个失败`);
              if (breakdown.imported) statusInfo.push(`${breakdown.imported}个已导入`);
            }

            if (statusInfo.length > 0) {
              errorMessage = `该任务共有${data.totalResults}条结果，但没有可导入的记录（${statusInfo.join('，')}）`;
            }
          }
        }
      }

      Message.error(errorMessage);
    }
  } catch (error) {
    console.error('Import error:', error);

    // 处理网络错误和其他异常
    if (error.response && error.response.data) {
      const errorData = error.response.data;
      Message.error(errorData.message || '导入失败');
    } else {
      Message.error('导入失败，请检查网络连接');
    }
  }
};

const viewResultDetail = record => {
  // 使用公共工具函数打开达人详情页面
  openInfluencerDetailPage(record, selectedTask.value);
};

// 格式化数字显示
const formatNumber = num => {
  if (!num) return '0';
  const number = parseInt(num);
  if (isNaN(number)) return num;

  if (number >= 10000) {
    return (number / 10000).toFixed(1) + 'w';
  }
  return number.toLocaleString();
};

// 设置播放量快捷值
const setPlayCountValue = value => {
  form.minFirstNotePlayCount = value;
};

// 格式化播放量显示
const formatPlayCountDisplay = count => {
  if (!count || count === 0) return '0';

  if (count >= 100000) {
    return `${(count / 10000).toFixed(1)}万`;
  } else if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}千`;
  } else {
    return `${count}`;
  }
};

// 显示播放量帮助说明
const showPlayCountHelp = () => {
  Message.info({
    content: `
      首个帖子播放量过滤功能说明：

      • 1千-5千：适用于新手达人或小众领域
      • 5千-1万：适用于中等影响力达人
      • 1万-5万：适用于有一定影响力的达人
      • 5万以上：适用于头部达人或热门内容

      设置越高，过滤越严格，获取的达人质量越高，但数量可能减少。
      建议根据实际需求和目标达人群体设置合适的阈值。
    `,
    duration: 8000
  });
};

// ===== 任务配置信息展示相关方法 =====

// 获取任务的minFirstNotePlayCount值
const getMinFirstNotePlayCount = task => {
  if (!task) return 1;
  return task.config?.minFirstNotePlayCount !== undefined ? task.config.minFirstNotePlayCount : 1;
};

// 获取优先级颜色
const getPriorityColor = priority => {
  const priorityMap = {
    0: 'gray',
    1: 'blue',
    2: 'orange',
    3: 'red'
  };
  return priorityMap[priority] || 'gray';
};

// 获取优先级文本
const getPriorityText = priority => {
  const priorityMap = {
    0: '低',
    1: '普通',
    2: '高',
    3: '紧急'
  };
  return priorityMap[priority] || '普通';
};

// 获取平台文本
const getPlatformText = platform => {
  const platformMap = {
    xiaohongshu: '小红书',
    douyin: '抖音',
    kuaishou: '快手',
    weibo: '微博'
  };
  return platformMap[platform] || platform;
};

// 获取其他配置参数（排除已单独展示的参数）
const getOtherConfigParams = task => {
  if (!task?.config) return {};

  const excludeKeys = ['minFirstNotePlayCount', 'pageSize', 'saveVideos', 'retries'];
  const otherParams = {};

  Object.keys(task.config).forEach(key => {
    if (!excludeKeys.includes(key)) {
      otherParams[key] = task.config[key];
    }
  });

  return otherParams;
};

// 获取配置参数的中文标签
const getConfigParamLabel = key => {
  const labelMap = {
    delay: '请求延迟',
    filters: '过滤条件',
    searchType: '搜索类型',
    minFollowers: '最少粉丝数',
    maxFollowers: '最多粉丝数',
    city: '城市限制',
    gender: '性别限制',
    ageRange: '年龄范围',
    tags: '标签过滤',
    contentType: '内容类型',
    publishTime: '发布时间',
    engagement: '互动率要求'
  };
  return labelMap[key] || key;
};

// 格式化配置值显示
const formatConfigValue = value => {
  if (value === null || value === undefined) return '-';
  if (typeof value === 'boolean') return value ? '是' : '否';
  if (typeof value === 'object') return JSON.stringify(value);
  if (typeof value === 'number') return value.toLocaleString();
  return String(value);
};

// ===== 配置参数输入辅助方法 =====

// 插入配置模板
const insertConfigTemplate = () => {
  const template = {
    searchType: 1,
    pageSize: 20,
    retries: 3,
    delay: {
      min: 1000,
      max: 3000
    }
  };

  try {
    configJson.value = JSON.stringify(template, null, 2);
    Message.success('配置模板已插入');
  } catch (error) {
    Message.error('插入模板失败');
  }
};

// 验证JSON格式
const validateConfigJson = () => {
  try {
    if (!configJson.value || configJson.value.trim() === '') {
      Message.info('配置为空，无需验证');
      return;
    }

    const parsed = JSON.parse(configJson.value);
    if (typeof parsed === 'object' && parsed !== null) {
      Message.success('JSON格式正确');
    } else {
      Message.error('配置必须是一个对象');
    }
  } catch (error) {
    Message.error(`JSON格式错误: ${error.message}`);
  }
};

// 清空配置
const clearConfig = () => {
  configJson.value = '';
  Message.success('配置已清空');
};

onMounted(() => {
  loadData();
  loadStats();
});
</script>

<style scoped>
.crawler-view {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 8px 0;
}

.page-description {
  color: #86909c;
  margin: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
}

.search-card {
  margin-bottom: 16px;
  border-radius: 8px;
}

.table-card {
  border-radius: 8px;
}

.results-content {
  padding: 16px 0;
}

.results-data {
  margin-top: 24px;
}

.results-data h4 {
  margin-bottom: 16px;
  color: #1d2129;
}

/* 播放量中位数样式 */
.play-mid-value {
  font-weight: 500;
  color: #1d2129;
}

.no-data {
  color: #86909c;
  font-style: italic;
}

/* 首个帖子播放量配置样式 */
.play-count-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-select-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.help-text {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  line-height: 1.4;
}

.formatted-display {
  padding: 8px 12px;
  background-color: #f2f8f2;
  border: 1px solid #b8e6b8;
  border-radius: 4px;
  display: inline-block;
}

.input-suffix {
  color: #86909c;
  font-size: 12px;
}

/* ===== 任务配置信息展示样式 ===== */
.task-config-section {
  margin: 16px 0;
}

.task-config-section .arco-collapse {
  background: #fafafa;
  border-radius: 6px;
}

.task-config-section .arco-collapse-item-header {
  font-weight: 600;
  color: #1d2129;
  background: #f2f3f5;
  border-radius: 6px 6px 0 0;
}

.config-content {
  padding: 8px 0;
}

.config-content .arco-descriptions {
  background: #fff;
}

.config-content .arco-descriptions-item-label {
  font-weight: 500;
  color: #4e5969;
  width: 140px;
}

.config-content .arco-descriptions-item-value {
  color: #1d2129;
}

/* 重点展示的配置项样式 */
.highlight-config .arco-descriptions-item-label {
  font-weight: 600;
  color: #165dff;
}

.play-count-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.play-count-display .formatted-value {
  font-weight: 600;
  color: #165dff;
  font-size: 14px;
}

.play-count-display .original-value {
  color: #86909c;
  font-size: 12px;
  font-style: italic;
}

/* ===== 配置参数输入区域样式 ===== */
.config-help {
  margin-top: 4px;
}

.config-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.config-actions .arco-btn {
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
}

.config-actions .arco-btn:hover {
  background-color: #f2f3f5;
}
</style>
