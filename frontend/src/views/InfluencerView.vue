<template>
  <div class="influencer-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">我的达人</h2>
        <p class="page-description">管理我的达人信息，支持增删改查操作</p>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <a-card class="search-card">
      <a-form :model="searchForm" layout="inline" @submit="handleSearch">
        <a-form-item label="达人昵称">
          <a-input v-model="searchForm.keyword" placeholder="请输入达人昵称" style="width: 160px" allow-clear />
        </a-form-item>
        <a-form-item label="平台">
          <a-select v-model="searchForm.platform" placeholder="请选择平台" style="width: 180px" allow-clear>
            <a-option value="xiaohongshu">小红书</a-option>
            <a-option value="juxingtu">巨量星图</a-option>
          </a-select>
        </a-form-item>
        <a-form-item label="分类">
          <a-input v-model="searchForm.category" placeholder="请输入分类" style="width: 120px" allow-clear />
        </a-form-item>
        <a-form-item label="星图/小红书ID">
          <a-input v-model="searchForm.platformId" placeholder="请输入星图ID" style="width: 200px" allow-clear />
        </a-form-item>
        <a-form-item label="粉丝数范围">
          <a-input-number
            v-model="searchForm.minFollowers"
            placeholder="最小值"
            style="width: 100px; margin-right: 8px"
            :min="0"
          />
          <span style="margin: 0 8px">-</span>
          <a-input-number v-model="searchForm.maxFollowers" placeholder="最大值" style="width: 100px" :min="0" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit">搜索</a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">重置</a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 达人列表 -->
    <a-card class="table-card">
      <div class="table-action">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <template #icon>
              <icon-plus />
            </template>
            新增达人
          </a-button>
        </a-space>
        <a-space> </a-space>
      </div>
      <a-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        class="layout-auto"
        :border="false"
        :scroll="{ x: 'max-content' }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <!-- 头像列 -->
        <template #avatar="{ record }">
          <a-avatar :size="40">
            <img v-if="record.avatarUrl" :src="record.avatarUrl" alt="avatar" @error="handleImageError" />
            <span v-else>{{ record.nickname?.charAt(0) }}</span>
          </a-avatar>
        </template>

        <!-- 平台列 -->
        <template #platform="{ record }">
          <a-tag :color="getPlatformColor(record.platform)">
            {{ getPlatformName(record.platform) }}
          </a-tag>
        </template>

        <!-- 粉丝数列 -->
        <template #followersCount="{ record }">
          <span>{{ formatNumber(record.followersCount) }}</span>
        </template>

        <!-- 达人报价列 -->
        <template #price="{ record }">
          <span>{{ formatPrice(record.authorExtInfo?.price_20_60) }}</span>
        </template>

        <!-- 达人标签列 -->
        <template #authorTags="{ record }">
          <div class="tags-container">
            <template v-if="record.influencerTags || record.authorExtInfo?.tags_relation">
              <div
                v-for="(subTags, mainTag) in parseTagsRelation(
                  record.influencerTags || record.authorExtInfo?.tags_relation
                )"
                :key="mainTag"
                class="tag-group"
              >
                <a-tag size="small" color="blue" style="margin-right: 4px; margin-bottom: 2px">
                  {{ mainTag }}
                </a-tag>
                <a-tag
                  v-for="subTag in subTags"
                  :key="subTag"
                  size="small"
                  color="green"
                  style="margin-right: 4px; margin-bottom: 2px"
                >
                  {{ subTag }}
                </a-tag>
              </div>
            </template>
          </div>
        </template>

        <!-- 内容主题列 -->
        <template #contentTheme="{ record }">
          <div class="content-theme-container">
            <a-tag
              v-for="theme in formatContentTheme(
                record.contentTheme || record.authorExtInfo?.content_theme_labels_180d
              )"
              :key="theme"
              size="small"
              color="blue"
              style="margin-right: 4px; margin-bottom: 2px"
            >
              {{ theme }}
            </a-tag>
          </div>
        </template>

        <!-- 标签列 -->
        <template #tags="{ record }">
          <a-tag
            v-for="tag in Array.isArray(record.tags) ? record.tags : []"
            :key="tag"
            size="small"
            style="margin-right: 4px"
          >
            {{ tag }}
          </a-tag>
        </template>

        <!-- 播放量中位数列 -->
        <template #playMid="{ record }">
          <span v-if="record.playMid">{{ formatNumber(record.playMid) }}</span>
          <span v-else class="text-gray-400">-</span>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-tag :color="record.status === 'active' ? 'green' : 'red'">
            {{ record.status === 'active' ? '活跃' : '不活跃' }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template #actions="{ record }">
          <a-space>
            <a-link type="text" size="small" @click="viewInfluencerPage(record)"> 达人主页 </a-link>
            <a-link type="text" size="small" @click="viewDetail(record)"> 查看 </a-link>
            <a-link type="text" size="small" @click="editInfluencer(record)"> 编辑 </a-link>
            <a-button type="text" size="small" @click="showReportForm(record)" status="success"> 提报 </a-button>
            <a-popconfirm content="确定要删除这个达人吗？" position="left" @ok="deleteInfluencer(record.id)">
              <a-link type="text" size="small" status="danger"> 删除 </a-link>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑达人弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '编辑达人' : '新增达人'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="达人昵称" field="nickname">
              <a-input v-model="form.nickname" placeholder="请输入达人昵称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="平台" field="platform">
              <a-select v-model="form.platform" placeholder="请选择平台">
                <a-option value="xiaohongshu">小红书</a-option>
                <a-option value="juxingtu">巨量星图</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="平台ID" field="platformId">
              <a-input v-model="form.platformId" placeholder="请输入平台用户ID" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="粉丝数" field="followersCount">
              <a-input-number v-model="form.followersCount" placeholder="请输入粉丝数" :min="0" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="分类" field="category">
              <a-input v-model="form.category" placeholder="请输入分类" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="头像URL" field="avatarUrl">
              <a-input v-model="form.avatarUrl" placeholder="请输入头像URL" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="标签" field="tags">
          <a-input v-model="tagsInput" placeholder="请输入标签，用逗号分隔" />
        </a-form-item>

        <a-form-item label="备注" field="notes">
          <a-textarea v-model="form.notes" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 达人提报表单 -->
    <InfluencerReportForm
      v-model:visible="reportFormVisible"
      :influencer-data="reportInfluencerData"
      @success="handleReportSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import { influencerAPI } from '@/services/api';
import { IconPlus } from '@arco-design/web-vue/es/icon';
import InfluencerReportForm from '@/components/InfluencerReportForm.vue';
import {
  openInfluencerDetailPage,
  getPlatformName,
  getPlatformColor,
  formatNumber,
  formatPrice,
  formatDate
} from '@/utils/platformUtils';

// 路由
const router = useRouter();

// 表格列定义
const columns = [
  {
    title: '头像',
    dataIndex: 'avatarUrl',
    slotName: 'avatar',
    width: 80
  },
  {
    title: '达人昵称',
    dataIndex: 'nickname'
  },
  {
    title: '平台',
    dataIndex: 'platform',
    slotName: 'platform'
  },
  {
    title: '平台ID',
    dataIndex: 'platformId'
  },
  {
    title: '粉丝数',
    dataIndex: 'followersCount',
    slotName: 'followersCount'
  },
  {
    title: '播放量/阅读量中位数',
    dataIndex: 'playMid',
    slotName: 'playMid',
    align: 'right'
  },
  {
    title: '达人报价',
    dataIndex: 'price',
    slotName: 'price',
    width: 120,
    align: 'right'
  },
  {
    title: '达人标签',
    dataIndex: 'authorTags',
    slotName: 'authorTags',
    width: 200,
    ellipsis: true
  },
  {
    title: '内容主题',
    dataIndex: 'contentTheme',
    slotName: 'contentTheme',
    width: 200,
    ellipsis: true
  },

  {
    title: '备注标签',
    dataIndex: 'tags',
    slotName: 'tags'
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt'
  },
  {
    title: '操作',
    slotName: 'actions',
    align: 'center',
    fixed: 'right'
  }
];

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const modalVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();

// 提报相关状态
const reportFormVisible = ref(false);
const reportInfluencerData = ref({});

// 搜索表单
const searchForm = reactive({
  keyword: '',
  platform: '',
  category: '',
  platformId: '',
  minFollowers: undefined,
  maxFollowers: undefined
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: true
});

// 表单数据
const form = reactive({
  id: '',
  nickname: '',
  platform: '',
  platformId: '',
  followersCount: 0,
  avatarUrl: '',
  category: '',
  notes: ''
});

// 标签输入字符串（用于表单输入）
const tagsInput = computed({
  get: () => (Array.isArray(form.tags) ? form.tags.join(', ') : ''),
  set: value => {
    form.tags = value
      ? value
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag)
      : [];
  }
});

// 表单验证规则
const rules = {
  nickname: [{ required: true, message: '请输入达人昵称' }],
  platform: [{ required: true, message: '请选择平台' }]
};

// 处理图片加载错误
const handleImageError = event => {
  const img = event.target;
  img.style.display = 'none';
};

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      sortBy: 'updatedAt',
      sortOrder: 'DESC',
      ...searchForm
    };

    const response = await influencerAPI.getList(params);
    if (response.success) {
      tableData.value = response.data;
      if (response.pagination) {
        pagination.total = response.pagination.total;
      }
    }
  } catch (error) {
    console.error('Load data error:', error);
    Message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    platform: '',
    category: '',
    platformId: '',
    minFollowers: undefined,
    maxFollowers: undefined
  });
  handleSearch();
};

// 分页变化
const handlePageChange = page => {
  pagination.current = page;
  loadData();
};

const handlePageSizeChange = pageSize => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  loadData();
};

// 显示新增弹窗
const showCreateModal = () => {
  isEdit.value = false;
  resetForm();
  modalVisible.value = true;
};

// 编辑达人
const editInfluencer = record => {
  isEdit.value = true;
  Object.assign(form, {
    ...record,
    tags: Array.isArray(record.tags) ? record.tags : []
  });
  modalVisible.value = true;
};

// 查看详情
const viewDetail = record => {
  router.push(`/influencers/${record.id}/detail`);
};

// 查看达人主页
const viewInfluencerPage = record => {
  // 使用公共工具函数打开达人详情页面
  openInfluencerDetailPage(record);
};

// 显示提报表单
const showReportForm = record => {
  reportInfluencerData.value = record;
  reportFormVisible.value = true;
};

// 提报成功回调
const handleReportSuccess = () => {
  // Message.success('提报成功');
  // 可以选择刷新数据或其他操作
  // 跳转到提报列表页面
  router.push('/influencer-reports');
};

// 删除达人
const deleteInfluencer = async id => {
  try {
    const response = await influencerAPI.delete(id);
    if (response.success) {
      Message.success('删除成功');
      loadData();
    }
  } catch (error) {
    console.error('Delete error:', error);
    Message.error('删除失败');
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate();
    if (valid) return;

    const submitData = {
      ...form,
      tags: Array.isArray(form.tags) ? form.tags : []
    };

    const apiCall = isEdit.value ? influencerAPI.update(form.id, submitData) : influencerAPI.create(submitData);

    const response = await apiCall;
    if (response.success) {
      Message.success(isEdit.value ? '更新成功' : '创建成功');
      modalVisible.value = false;
      loadData();
    }
  } catch (error) {
    console.error('Submit error:', error);
    Message.error('操作失败');
  }
};

// 取消操作
const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    nickname: '',
    platform: '',
    platformId: '',
    followersCount: 0,
    avatarUrl: '',
    category: '',
    notes: ''
  });
};

// 格式化函数
const formatTags = tagsData => {
  if (!tagsData) return [];
  if (Array.isArray(tagsData)) return tagsData.slice(0, 3); // 最多显示3个标签
  if (typeof tagsData === 'string') {
    try {
      const parsed = JSON.parse(tagsData);
      return Array.isArray(parsed) ? parsed.slice(0, 3) : [];
    } catch {
      return [tagsData];
    }
  }
  return [];
};

const formatContentTheme = themeData => {
  if (!themeData) return [];
  if (Array.isArray(themeData)) return themeData.slice(0, 3); // 最多显示3个主题
  if (typeof themeData === 'string') {
    try {
      const parsed = JSON.parse(themeData);
      return Array.isArray(parsed) ? parsed.slice(0, 3) : [];
    } catch {
      return [themeData];
    }
  }
  return [];
};

// 解析标签关系数据
const parseTagsRelation = tagsData => {
  if (!tagsData) return {};

  // 如果是字符串，尝试解析为JSON
  if (typeof tagsData === 'string') {
    try {
      const parsed = JSON.parse(tagsData);
      // 如果解析后是数组（小红书格式），转换为对象格式
      if (Array.isArray(parsed)) {
        return { 标签: parsed };
      }
      return parsed;
    } catch {
      return {};
    }
  }

  // 如果是数组（小红书的influencerTags直接是数组格式）
  if (Array.isArray(tagsData)) {
    return { 标签: tagsData };
  }

  // 如果已经是对象，直接返回
  if (typeof tagsData === 'object' && tagsData !== null) {
    return tagsData;
  }

  return {};
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.influencer-view {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 8px 0;
}

.page-description {
  color: #86909c;
  margin: 0;
}

.search-card {
  margin-bottom: 16px;
  border-radius: 8px;
}

.table-card {
  border-radius: 8px;
}

.tags-container {
  max-width: 180px;
  display: flex;
  flex-wrap: wrap;
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 4px;
}

.content-theme-container {
  max-width: 180px;
  display: flex;
  flex-wrap: wrap;
}
</style>
