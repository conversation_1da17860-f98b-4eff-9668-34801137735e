<template>
  <a-modal
    :visible="visible"
    title="智能达人提报"
    width="auto"
    :footer="false"
    @cancel="handleCancel"
    @update:visible="handleCancel"
    unmount-on-close
  >
    <!-- 第一阶段：达人信息预解析 -->
    <div v-if="currentStep === 1" class="step-content">
      <!-- <div class="step-header">
        <h3>第一步：输入达人信息</h3>
        <p>请输入达人主页链接或平台用户ID</p>
      </div> -->

      <a-form :model="parseForm" layout="vertical">
        <a-form-item label="达人链接或ID" required>
          <a-textarea
            v-model="parseForm.input"
            placeholder="支持以下格式：&#10;• 小红书蒲公英链接：https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/xxx&#10;• 巨量星图链接：https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/xxx&#10;• 直接输入平台用户ID"
            :auto-size="{
              minRows: 4,
              maxRows: 8
            }"
            :max-length="500"
            show-word-limit
          />
        </a-form-item>

        <!-- 解析结果显示 -->
        <div v-if="parseResult.platform" class="parse-result success">
          <a-alert type="success" show-icon>
            <template #icon>
              <icon-check-circle />
            </template>
            <div>
              <p><strong>解析成功！</strong></p>
              <p>
                平台：<a-tag :color="getPlatformColor(parseResult.platform)">{{
                  getPlatformName(parseResult.platform)
                }}</a-tag>
              </p>
              <p>
                用户ID：<code>{{ parseResult.platformUserId }}</code>
              </p>
            </div>
          </a-alert>
        </div>

        <div v-if="parseResult.error" class="parse-result error">
          <a-alert type="error" show-icon>
            <template #icon>
              <icon-close-circle />
            </template>
            <div>
              <p><strong>解析失败</strong></p>
              <p>{{ parseResult.error }}</p>
            </div>
          </a-alert>
        </div>
      </a-form>

      <div class="step-actions">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleParseInput" :loading="parsing"> 解析并继续 </a-button>
        </a-space>
      </div>
    </div>

    <!-- 第二阶段：达人数据拉取 -->
    <div v-if="currentStep === 2" class="step-content">
      <div class="step-header">
        <h3>第二步：获取达人数据</h3>
        <p>正在从{{ getPlatformName(parseResult.platform) }}平台获取达人信息...</p>
      </div>

      <!-- 数据拉取进度 -->
      <div class="data-fetch-progress">
        <a-steps :current="fetchStep" size="small">
          <a-step title="获取基础信息" :status="getStepStatus(0)" />
          <a-step title="获取作品数据" :status="getStepStatus(1)" />
          <a-step title="数据处理完成" :status="getStepStatus(2)" />
        </a-steps>

        <div v-if="fetchLoading" class="loading-info">
          <a-spin size="large">
            <div class="loading-text">
              <a-spin />
              {{ fetchStatusText }}
            </div>
          </a-spin>
        </div>
      </div>

      <!-- 达人基础信息预览 -->
      <div v-if="authorInfo" class="author-preview">
        <h4>达人信息预览</h4>
        <a-descriptions :column="3" bordered size="small">
          <a-descriptions-item label="达人昵称">
            <!-- 图片头像 -->
            <a-avatar :size="48" class="influencer-avatar">
              <img v-if="authorInfo.avatarUrl" :src="authorInfo.avatarUrl" alt="avatar" @error="handleImageError" />
              <span v-else class="avatar-text">
                {{ authorInfo.influencerName?.charAt(0) }}
              </span>
            </a-avatar>
            {{ authorInfo.influencerName }}</a-descriptions-item
          >
          <a-descriptions-item label="粉丝数量">{{ formatNumber(authorInfo.followersCount) }}</a-descriptions-item>
          <a-descriptions-item label="中位播放量">{{ formatNumber(authorInfo.playMid) }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 作品库关联选项 -->
      <div v-if="authorInfo && !fetchLoading" class="video-association">
        <a-checkbox v-model="associateVideos"> 关联作品库（将该达人的作品保存到系统作品库中） </a-checkbox>
        <p class="association-tip">勾选后，系统将保存该达人的所有作品数据，便于后续分析和管理</p>
      </div>

      <div v-if="!fetchLoading" class="step-actions">
        <a-space>
          <a-button @click="goToPreviousStep">上一步</a-button>
          <a-button type="primary" @click="goToNextStep" :disabled="!authorInfo"> 下一步 </a-button>
        </a-space>
      </div>
    </div>

    <!-- 第三阶段：作品筛选与关联 -->
    <div v-if="currentStep === 3" class="step-content">
      <div class="step-header">
        <h3>第三步：选择关联作品</h3>
        <p>从该达人的作品中选择要关联到提报单的内容</p>
      </div>

      <!-- 作品筛选工具 -->
      <div class="video-filter">
        <a-input-search
          v-model="videoFilter"
          placeholder="输入关键词筛选作品标题..."
          style="width: 300px"
          @search="filterVideos"
        />
        <div class="filter-stats">
          共 {{ filteredVideos.length }} 个作品
          <span v-if="rowSelection.selectedRowKeys.length > 0"
            >，已选择 {{ rowSelection.selectedRowKeys.length }} 个</span
          >
        </div>
      </div>

      <!-- 作品列表 -->
      <div class="video-list">
        <a-table
          :data="filteredVideos"
          :pagination="{ pageSize: 10 }"
          :scroll="{ x: 'max-content' }"
          row-key="videoId"
          :row-selection="rowSelection"
          @selection-change="selectionChange"
        >
          <template #columns>
            <a-table-column
              title="作品标题"
              data-index="title"
              :sortable="{
                sortDirections: ['ascend', 'descend']
              }"
            >
              <template #cell="{ record }">
                <div class="video-title">
                  <a-tooltip :content="record.title">
                    {{ record.title }}
                  </a-tooltip>
                </div>
              </template>
            </a-table-column>
            <a-table-column
              title="播放量"
              data-index="playCount"
              :width="100"
              :sortable="{
                sortDirections: ['ascend', 'descend']
              }"
            >
              <template #cell="{ record }">
                {{ formatNumber(record.playCount) }}
              </template>
            </a-table-column>
            <a-table-column
              title="点赞量"
              data-index="likeCount"
              :width="100"
              :sortable="{
                sortDirections: ['ascend', 'descend']
              }"
            >
              <template #cell="{ record }">
                {{ formatNumber(record.likeCount) }}
              </template>
            </a-table-column>
            <a-table-column
              title="收藏量"
              data-index="collectCount"
              :width="100"
              :sortable="{
                sortDirections: ['ascend', 'descend']
              }"
            >
              <template #cell="{ record }">
                {{ formatNumber(record.collectCount) }}
              </template>
            </a-table-column>
            <a-table-column
              title="发布时间"
              data-index="publishTime"
              :sortable="{
                sortDirections: ['ascend', 'descend']
              }"
            >
              <template #cell="{ record }">
                {{ formatDate(record.publishTime) }}
              </template>
            </a-table-column>
            <a-table-column title="操作" :width="120" align="center" fixed="right">
              <template #cell="{ record }">
                <a-button
                  type="text"
                  size="small"
                  @click="viewVideo(record)"
                  :loading="loadingNotes[record.videoId]"
                  :disabled="!record.videoUrl && parseResult.platform !== 'xiaohongshu'"
                >
                  观看
                </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>

      <div class="step-actions">
        <a-space>
          <a-button @click="goToPreviousStep">上一步</a-button>
          <a-button type="primary" @click="goToNextStep"> 下一步（创建提报单） </a-button>
        </a-space>
      </div>
    </div>

    <!-- 第四阶段：提报单创建 -->
    <div v-if="currentStep === 4" class="step-content">
      <div class="step-header">
        <h3>第四步：完善提报信息</h3>
        <p>系统已自动填充达人信息，请完善其他必要信息</p>
      </div>

      <!-- 直接在这里实现提报表单 -->
      <a-form ref="reportFormRef" :model="reportForm" :rules="reportRules" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="平台" field="platform">
              <a-select v-model="reportForm.platform" placeholder="请选择平台" disabled>
                <a-option value="xiaohongshu">小红书</a-option>
                <a-option value="juxingtu">巨量星图</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="达人名称" field="influencerName">
              <a-input v-model="reportForm.influencerName" placeholder="请输入达人名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="运营负责人" field="operationManager">
              <a-input v-model="reportForm.operationManager" placeholder="请输入运营负责人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="粉丝量" field="followersCount">
              <a-input-number
                v-model="reportForm.followersCount"
                placeholder="请输入粉丝量"
                :min="0"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="达人主页链接" field="influencerUrl">
              <a-input v-model="reportForm.influencerUrl" placeholder="请输入达人主页链接" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="播放量中位数" field="playMid">
              <a-input v-model="reportForm.playMid" placeholder="请输入播放量中位数" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="平台达人报价" field="platformPrice">
              <a-textarea v-model="reportForm.platformPrice" placeholder="请输入平台达人报价" :rows="2" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="合作价格" field="cooperationPrice">
              <a-textarea v-model="reportForm.cooperationPrice" placeholder="请输入合作价格" :rows="2" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="选择理由" field="selectionReason">
          <a-textarea
            v-model="reportForm.selectionReason"
            placeholder="请输入选择理由（必填）"
            :rows="3"
            :max-length="500"
            show-word-limit
          />
        </a-form-item>

        <a-form-item label="备注" field="notes">
          <a-textarea
            v-model="reportForm.notes"
            placeholder="请输入备注（可选）"
            :rows="2"
            :max-length="200"
            show-word-limit
          />
        </a-form-item>

        <!-- 关联作品展示 -->
        <a-form-item v-if="rowSelection.selectedRowKeys.length > 0 || authorVideos.length > 0" label="关联作品">
          <div class="related-videos">
            <div class="videos-header">
              <span v-if="rowSelection.selectedRowKeys.length > 0"
                >已选择 {{ rowSelection.selectedRowKeys.length }} 个作品</span
              >
              <span v-else>暂未选择作品</span>
              <div class="videos-actions">
                <a-button v-if="authorVideos.length > 0" type="text" size="small" @click="goToVideoSelection">
                  {{ rowSelection.selectedRowKeys.length > 0 ? '重新选择' : '选择作品' }}
                </a-button>
              </div>
            </div>
            <div v-if="rowSelection.selectedRowKeys.length > 0" class="videos-list">
              <div
                v-for="video in getSelectedVideoDetails()"
                :key="video.videoId"
                class="video-item clickable"
                @click="previewVideo(video)"
              >
                <div class="video-content">
                  <div class="video-title">{{ video.title }}</div>
                  <div class="video-stats">
                    <span>播放: {{ formatNumber(video.playCount) }}</span>
                    <span>点赞: {{ formatNumber(video.likeCount) }}</span>
                    <span>收藏: {{ formatNumber(video.collectCount) }}</span>
                  </div>
                </div>
                <div class="video-actions">
                  <a-button type="text" size="small" status="danger" @click.stop="removeVideo(video.videoId)">
                    删除
                  </a-button>
                </div>
              </div>
            </div>
            <div v-else-if="authorVideos.length === 0" class="no-videos">
              <a-empty description="暂无作品数据" />
            </div>
          </div>
        </a-form-item>
      </a-form>

      <div class="step-actions">
        <a-space>
          <a-button @click="goToPreviousStep">上一步</a-button>
          <a-button type="primary" @click="handleSubmitReport" :loading="submittingReport"> 提交提报 </a-button>
        </a-space>
      </div>
    </div>

    <!-- 作品预览模态窗口 -->
    <a-modal
      v-model:visible="videoPreviewVisible"
      title="作品预览"
      width="600px"
      :footer="false"
      @cancel="closeVideoPreview"
    >
      <div v-if="currentPreviewVideo" class="video-preview">
        <div class="video-info">
          <h3>{{ currentPreviewVideo.title }}</h3>
          <div class="video-stats-detail">
            <a-descriptions :column="2" size="small">
              <a-descriptions-item label="发布时间">
                {{ formatDate(currentPreviewVideo.publishTime) }}
              </a-descriptions-item>
              <a-descriptions-item label="播放量">
                {{ formatNumber(currentPreviewVideo.playCount) }}
              </a-descriptions-item>
              <a-descriptions-item label="点赞量">
                {{ formatNumber(currentPreviewVideo.likeCount) }}
              </a-descriptions-item>
              <a-descriptions-item label="收藏量">
                {{ formatNumber(currentPreviewVideo.collectCount) }}
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <div v-if="currentPreviewVideo.description" class="video-description">
            <h4>作品描述</h4>
            <p>{{ currentPreviewVideo.description }}</p>
          </div>

          <div class="video-actions">
            <a-space>
              <a-button
                type="primary"
                @click="viewVideo(currentPreviewVideo)"
                :loading="loadingNotes[currentPreviewVideo?.videoId]"
                :disabled="!currentPreviewVideo.videoUrl && parseResult.platform !== 'xiaohongshu'"
              >
                <template #icon>
                  <icon-play-arrow />
                </template>
                观看原作品
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconCheckCircle, IconCloseCircle, IconPlayArrow, IconCopy } from '@arco-design/web-vue/es/icon';
import { useUserStore } from '@/stores/user';
import {
  parseInfluencerInput,
  getPlatformName,
  getPlatformColor,
  formatNumber,
  formatDate
} from '@/utils/platformUtils';
import { influencerReportAPI, authorVideoAPI } from '@/services/api';

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  // 预填充的达人数据（从达人页面跳转时使用）
  influencerData: {
    type: Object,
    default: () => ({})
  },
  // 是否直接跳转到第四阶段
  skipToStep4: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:visible', 'success', 'cancel']);

// 用户状态
const userStore = useUserStore();

// 响应式数据
const currentStep = ref(1);
const parsing = ref(false);
const fetchLoading = ref(false);
const fetchStep = ref(0);
const fetchStatusText = ref('');

// 第一阶段：解析表单
const parseForm = reactive({
  input: ''
});

const parseResult = reactive({
  platform: '',
  platformUserId: '',
  error: ''
});

// 第二阶段：达人信息
const authorInfo = ref(null);
const authorVideos = ref([]);
const associateVideos = ref(false);

// 第三阶段：作品筛选
const videoFilter = ref('');
const selectedVideos = ref([]);

// 作品预览相关
const videoPreviewVisible = ref(false);
const currentPreviewVideo = ref(null);
const loadingNotes = ref({}); // 用于跟踪每个帖子的加载状态

// 第四阶段：提报表单
const reportFormRef = ref();
const submittingReport = ref(false);
const reportForm = reactive({
  platform: '',
  influencerName: '',
  operationManager: '',
  influencerUrl: '',
  followersCount: 0,
  playMid: '',
  selectionReason: '',
  platformPrice: '',
  cooperationPrice: '',
  notes: '',
  platformUserId: '',
  relatedVideos: []
});

const reportRules = {
  platform: [{ required: true, message: '请选择平台' }],
  influencerName: [{ required: true, message: '请输入达人名称' }],
  operationManager: [{ required: true, message: '请输入运营负责人' }],
  selectionReason: [
    { required: true, message: '请输入选择理由' },
    { minLength: 10, message: '选择理由至少10个字符' }
  ]
};

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  selectedRowKeys: [],
  selectedRows: []
});
const selectionChange = selectedRowKeys => {
  rowSelection.selectedRowKeys = selectedRowKeys;
  rowSelection.selectedRows = authorVideos.value.filter(video => selectedRowKeys.includes(video.videoId));
};

// 计算属性
const filteredVideos = computed(() => {
  if (!videoFilter.value) return authorVideos.value;
  return authorVideos.value.filter(
    video => video.title && video.title.toLowerCase().includes(videoFilter.value.toLowerCase())
  );
});

// 监听步骤变化，自动填充表单
watch(
  () => currentStep.value,
  newStep => {
    if (newStep === 4 && (authorInfo.value || props.influencerData)) {
      // 进入第四步时，自动填充表单数据
      fillReportForm();
    }
  }
);

// 监听 visible 变化，处理直接跳转逻辑
watch(
  () => props.visible,
  newVisible => {
    if (newVisible && props.skipToStep4 && props.influencerData) {
      // 直接跳转到第四阶段
      initFromInfluencerData();
    }
  }
);

// 从达人数据初始化
const initFromInfluencerData = () => {
  if (!props.influencerData) return;

  // 设置解析结果
  Object.assign(parseResult, {
    platform: props.influencerData.platform || 'xiaohongshu',
    platformUserId: props.influencerData.platformUserId || props.influencerData.platformId,
    error: ''
  });

  // 设置达人信息
  authorInfo.value = {
    nickname: props.influencerData.nickname || props.influencerData.influencerName || '未知达人',
    platformUserId: props.influencerData.platformUserId || props.influencerData.platformId,
    platform: props.influencerData.platform || 'xiaohongshu',
    followersCount: props.influencerData.followersCount || 0,
    videoCount: props.influencerData.videoCount || 0,
    averagePlay: props.influencerData.averagePlay || props.influencerData.playMid || 0,
    priceInfo: props.influencerData.priceInfo || null,
    rawData: props.influencerData
  };

  // 直接跳转到第四阶段
  currentStep.value = 4;
};

// 监听输入变化，实时解析
watch(
  () => parseForm.input,
  newValue => {
    if (newValue) {
      const result = parseInfluencerInput(newValue);
      Object.assign(parseResult, result);
    } else {
      Object.assign(parseResult, { platform: '', platformUserId: '', error: '' });
    }
  }
);

// 方法
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
  resetModal();
};

const resetModal = () => {
  currentStep.value = 1;
  parseForm.input = '';
  Object.assign(parseResult, { platform: '', platformUserId: '', error: '' });
  authorInfo.value = null;
  authorVideos.value = [];
  rowSelection.selectedRowKeys = [];
  rowSelection.selectedRows = [];
  videoFilter.value = '';
  associateVideos.value = false;

  // 重置提报表单
  Object.assign(reportForm, {
    platform: '',
    influencerName: '',
    operationManager: '',
    influencerUrl: '',
    followersCount: 0,
    playMid: '',
    selectionReason: '',
    platformPrice: '',
    cooperationPrice: '',
    notes: '',
    platformUserId: '',
    relatedVideos: []
  });

  submittingReport.value = false;

  // 重置预览状态
  videoPreviewVisible.value = false;
  currentPreviewVideo.value = null;
  loadingNotes.value = {};
};

const handleParseInput = async () => {
  if (parseResult.error) {
    Message.error(parseResult.error);
    return;
  }

  if (!parseResult.platform || !parseResult.platformUserId) {
    Message.error('请先输入有效的达人链接或ID');
    return;
  }

  // 先检查Cookie状态
  try {
    parsing.value = true;
    const cookieStatus = await influencerReportAPI.checkCookieStatus({
      platform: parseResult.platform
    });

    if (cookieStatus.success && !cookieStatus.data.hasAvailableCookie) {
      Message.error(cookieStatus.data.message);
      return;
    }

    currentStep.value = 2;
    fetchAuthorData();
  } catch (error) {
    console.error('检查Cookie状态失败:', error);
    Message.error('系统检查失败，请稍后重试');
  } finally {
    parsing.value = false;
  }
};

const goToPreviousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const goToNextStep = () => {
  if (currentStep.value < 4) {
    currentStep.value++;
  }
};

const getStepStatus = step => {
  if (step < fetchStep.value) return 'finish';
  if (step === fetchStep.value) return 'process';
  return 'wait';
};

// 获取达人数据
const fetchAuthorData = async () => {
  fetchLoading.value = true;
  fetchStep.value = 0;
  fetchStatusText.value = '正在获取达人基础信息...';

  try {
    // 第一步：获取达人基础信息
    const authorResponse = await influencerReportAPI.getAuthorInfo({
      platform: parseResult.platform,
      authorId: parseResult.platformUserId
    });

    if (authorResponse.success) {
      authorInfo.value = authorResponse.data;
      fetchStep.value = 1;
      fetchStatusText.value = '正在获取作品数据...';

      // 第二步：获取作品数据
      const videosResponse = await influencerReportAPI.getAuthorVideos({
        platform: parseResult.platform,
        authorId: parseResult.platformUserId,
        associateVideos: associateVideos.value
      });

      if (videosResponse.success) {
        authorVideos.value = videosResponse.data.notes || [];
        fetchStep.value = 2;
        fetchStatusText.value = '数据处理完成';

        setTimeout(() => {
          fetchLoading.value = false;
        }, 1000);
      } else {
        // 特殊处理Cookie相关错误
        if (videosResponse.message && videosResponse.message.includes('Cookie配置')) {
          throw new Error('系统Cookie配置异常，请联系管理员或稍后重试');
        }
        throw new Error(videosResponse.message || '获取作品数据失败');
      }
    } else {
      // 特殊处理Cookie相关错误
      if (authorResponse.message && authorResponse.message.includes('Cookie配置')) {
        throw new Error('系统Cookie配置异常，请联系管理员或稍后重试');
      }
      throw new Error(authorResponse.message || '获取达人信息失败');
    }
  } catch (error) {
    console.error('获取达人数据失败:', error);
    Message.error(error.message || '获取达人数据失败，请重试');
    fetchLoading.value = false;
  }
};

// 作品筛选
const filterVideos = () => {
  // 筛选逻辑已在计算属性中实现
};

const getSelectedVideoDetails = () => {
  return authorVideos.value.filter(video => rowSelection.selectedRowKeys.includes(video.videoId));
};

// 工具方法
const generateInfluencerUrl = () => {
  if (parseResult.platform === 'xiaohongshu') {
    return `https://pgy.xiaohongshu.com/solar/pre-trade/blogger-detail/${parseResult.platformUserId}?source=Advertiser_Kol`;
  } else if (parseResult.platform === 'juxingtu') {
    return `https://www.xingtu.cn/ad/creator/author-homepage/douyin-video/${parseResult.platformUserId}`;
  }
  return '';
};

const formatPriceInfo = priceInfo => {
  if (!priceInfo || typeof priceInfo !== 'object') return '';

  const parts = [];
  if (priceInfo.post) parts.push(`图文: ${priceInfo.post}元`);
  if (priceInfo.video) parts.push(`视频: ${priceInfo.video}元`);
  if (priceInfo.live) parts.push(`直播: ${priceInfo.live}元`);

  return parts.join('; ');
};

// 填充提报表单
const fillReportForm = async () => {
  // 确保用户信息已加载
  if (!userStore.user && userStore.token) {
    await userStore.getCurrentUser();
  }

  // 优先使用 authorInfo，如果没有则使用 props.influencerData
  const dataSource = authorInfo.value || props.influencerData;
  if (!dataSource) return;

  Object.assign(reportForm, {
    platform: parseResult.platform || dataSource.platform || 'xiaohongshu',
    influencerName: dataSource.nickname || dataSource.influencerName || '未知达人',
    operationManager: userStore.user?.chineseName || '未知用户',
    platformUserId: parseResult.platformUserId || dataSource.platformUserId || dataSource.platformId,
    influencerUrl: generateInfluencerUrl() || dataSource.influencerUrl || '',
    followersCount: dataSource.followersCount || 0,
    playMid: dataSource.averagePlay?.toString() || dataSource.playMid?.toString() || '',
    platformPrice: dataSource.priceInfo ? formatPriceInfo(dataSource.priceInfo) : dataSource.platformPrice || '',
    cooperationPrice: dataSource.cooperationPrice || '',
    selectionReason: '',
    notes: '',
    relatedVideos: getSelectedVideoDetails()
  });
};

// 提交提报
const handleSubmitReport = async () => {
  try {
    // 表单验证
    const valid = await reportFormRef.value?.validate();
    if (valid) return;

    submittingReport.value = true;

    // 准备提交数据
    const submitData = {
      ...reportForm,
      relatedVideos: getSelectedVideoDetails()
    };

    console.log('提交智能提报数据:', submitData);

    // 调用创建接口
    const response = await influencerReportAPI.create(submitData);

    if (response.success) {
      Message.success('智能提报创建成功');
      emit('success');
      handleCancel();
    } else {
      Message.error(response.message || '提报创建失败');
    }
  } catch (error) {
    console.error('提交提报失败:', error);
    Message.error(error.response?.data?.message || '提报创建失败');
  } finally {
    submittingReport.value = false;
  }
};

// 作品预览方法
const previewVideo = video => {
  currentPreviewVideo.value = video;
  videoPreviewVisible.value = true;
};

const closeVideoPreview = () => {
  videoPreviewVisible.value = false;
  currentPreviewVideo.value = null;
};

// 作品观看相关方法
const viewVideo = async video => {
  // 如果是小红书平台，调用API获取最新详情
  if (parseResult.platform === 'xiaohongshu' && video.videoId) {
    await handleXiaohongshuWatch(video);
  } else if (video.videoUrl) {
    // 其他平台直接打开链接
    window.open(video.videoUrl, '_blank');
  } else {
    Message.warning('该作品暂无观看链接');
  }
};

/**
 * 处理小红书帖子观看按钮点击
 * 调用API获取最新帖子详情并打开链接
 */
const handleXiaohongshuWatch = async video => {
  try {
    // 设置加载状态
    loadingNotes.value[video.videoId] = true;

    // 调用API获取帖子详情
    const response = await authorVideoAPI.getXiaohongshuNoteDetail(video.videoId);

    if (response.success && response.data) {
      // 打开帖子链接
      window.open(response.data.noteLink, '_blank');

      // 显示成功消息
      Message.success('已打开最新帖子链接');
    } else {
      // 显示错误消息
      Message.error(response.message || '获取帖子详情失败');
    }
  } catch (error) {
    console.error('获取小红书帖子详情失败:', error);
    Message.error('获取帖子详情失败，请稍后重试');
  } finally {
    // 清除加载状态
    loadingNotes.value[video.videoId] = false;
  }
};

const copyVideoUrl = async video => {
  if (!video.videoUrl) {
    Message.warning('该作品暂无链接可复制');
    return;
  }

  try {
    await navigator.clipboard.writeText(video.videoUrl);
    Message.success('链接已复制到剪贴板');
  } catch (error) {
    console.error('复制链接失败:', error);

    // 降级方案：使用传统的复制方法
    try {
      const textArea = document.createElement('textarea');
      textArea.value = video.videoUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      Message.success('链接已复制到剪贴板');
    } catch (fallbackError) {
      Message.error('复制链接失败，请手动复制');
      console.error('降级复制方案也失败:', fallbackError);
    }
  }
};

// 作品管理相关方法
const goToVideoSelection = () => {
  // 返回第三阶段重新选择作品
  currentStep.value = 3;
};

const removeVideo = videoId => {
  // 从选中列表中移除指定作品
  const index = rowSelection.selectedRowKeys.indexOf(videoId);
  if (index > -1) {
    rowSelection.selectedRowKeys.splice(index, 1);
    // 同时更新 selectedRows
    rowSelection.selectedRows = rowSelection.selectedRows.filter(video => video.videoId !== videoId);
    Message.success('作品已移除');
  }
};

const handleReportSuccess = () => {
  emit('success');
  handleCancel();
};
</script>

<style scoped>
.step-content {
  min-height: 200px;
  min-width: 500px;
}

.step-header {
  margin-bottom: 24px;
  text-align: center;
}

.step-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.step-header p {
  margin: 0;
  color: #666;
}

.parse-result {
  margin-top: 16px;
}

.step-actions {
  margin-top: 24px;
  text-align: center;
}

.data-fetch-progress {
  margin: 24px 0;
}

.loading-info {
  text-align: center;
  margin: 24px 0;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 16px;
  color: #666;
}

.author-preview {
  margin: 24px 0;
}

.video-association {
  margin: 24px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.association-tip {
  margin: 8px 0 0 24px;
  color: #666;
  font-size: 12px;
}

.video-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-stats {
  color: #666;
  font-size: 14px;
}

.video-list {
  margin: 16px 0;
}

.video-title {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.embedded-form {
  /* 嵌入表单容器样式 */
  width: 100%;
}

.embedded-form :deep(.arco-modal) {
  position: static;
  width: 100%;
  margin: 0;
  box-shadow: none;
}

.embedded-form :deep(.arco-modal-container) {
  position: static;
  padding: 0;
}

.embedded-form :deep(.arco-modal-header) {
  display: none;
}

.embedded-form :deep(.arco-modal-footer) {
  display: none;
}

.embedded-form :deep(.arco-modal-body) {
  padding: 0;
}

.related-videos {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
  background-color: #f8f9fa;
  width: 100%;
}

.videos-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #1d2129;
}

.videos-actions {
  display: flex;
  gap: 8px;
}

.videos-list {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  gap: 10px;
}

.video-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
  transition: all 0.2s ease;
}

.video-item.clickable {
  cursor: pointer;
}

.video-item.clickable:hover {
  border-color: #165dff;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
}

.video-content {
  flex: 1;
  min-width: 0;
}

.video-actions {
  flex-shrink: 0;
  margin-left: 12px;
}

.video-item:last-child {
  margin-bottom: 0;
}

.video-title {
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #86909c;
}

.video-stats span {
  display: flex;
  align-items: center;
}

/* 作品预览样式 */
.video-preview {
  padding: 16px 0;
}

.video-info h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  line-height: 1.4;
}

.video-stats-detail {
  margin-bottom: 20px;
}

.video-description {
  margin-bottom: 20px;
}

.video-description h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
}

.video-description p {
  margin: 0;
  color: #4e5969;
  line-height: 1.6;
  word-break: break-word;
  max-height: 120px;
  overflow-y: auto;
}

.no-videos {
  text-align: center;
  padding: 20px;
  color: #86909c;
}
</style>
